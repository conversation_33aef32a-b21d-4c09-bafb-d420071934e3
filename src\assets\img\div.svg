<svg width="466" height="300" viewBox="0 0 466 300" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_59_17574)">
<rect x="16.8805" y="31.4749" width="217.547" height="0.98369" fill="#1F2D3A"/>
<rect x="17.0642" y="31.6586" width="217.18" height="0.61623" stroke="#324867" stroke-width="0.367461"/>
</g>
<rect x="234.611" y="31.0031" width="52.3811" height="1.27202" fill="#0665FD" stroke="#00A0FF" stroke-width="0.367461"/>
<rect x="234.427" y="30.8193" width="0.327631" height="0.327897" fill="#02E3FF"/>
<rect x="286.849" y="30.8193" width="0.327631" height="0.327897" fill="#02E3FF"/>
<rect x="234.427" y="32.1301" width="0.327631" height="0.327897" fill="#02E3FF"/>
<rect x="286.849" y="32.1301" width="0.327631" height="0.327897" fill="#02E3FF"/>
<rect x="241.164" y="31.0031" width="16.3417" height="1.27202" fill="#6FFDFE" stroke="#B1FFFF" stroke-width="0.367461"/>
<rect x="240.98" y="30.8193" width="0.327631" height="0.327897" fill="#FFFEFF"/>
<rect x="240.98" y="32.1301" width="0.327631" height="0.327897" fill="#FFFEFF"/>
<rect x="257.361" y="30.8193" width="0.327631" height="0.327897" fill="#FFFEFF"/>
<rect x="257.361" y="32.1301" width="0.327631" height="0.327897" fill="#FFFEFF"/>
<rect x="240.652" y="32.1304" width="0.327631" height="0.327897" fill="#0140FF"/>
<rect x="240.652" y="30.8193" width="0.327631" height="0.327897" fill="#0140FF"/>
<rect x="240.652" y="31.147" width="0.327631" height="0.98369" fill="#0007FC"/>
<rect x="257.689" y="32.1304" width="0.327631" height="0.327897" fill="#0140FF"/>
<rect x="257.689" y="30.8193" width="0.327631" height="0.327897" fill="#0140FF"/>
<rect x="257.689" y="31.147" width="0.327631" height="0.98369" fill="#0007FC"/>
<rect x="234.1" y="31.1467" width="0.327631" height="0.327897" fill="#472301"/>
<rect x="234.1" y="31.8025" width="0.327631" height="0.327897" fill="#472301"/>
<rect x="234.1" y="31.4749" width="0.327631" height="0.327897" fill="#2F0F02"/>
<g opacity="0.1" filter="url(#filter1_f_59_17574)">
<ellipse cx="249.334" cy="31.4745" rx="9.33748" ry="7.86952" fill="#71FFCC"/>
</g>
<g filter="url(#filter2_d_59_17574)">
<rect x="30.9681" y="24.2607" width="193.63" height="4.59055" fill="#062E64" fill-opacity="0.25" shape-rendering="crispEdges"/>
<rect x="31.1519" y="24.4445" width="193.262" height="4.22309" stroke="#081832" stroke-width="0.367461" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter3_f_59_17574)">
<ellipse cx="16.3888" cy="29.3437" rx="6.3888" ry="10.6566" fill="#03F1FD"/>
</g>
<g filter="url(#filter4_d_59_17574)">
<rect x="14.9133" y="20.6538" width="2.94868" height="14.4275" fill="url(#paint0_linear_59_17574)"/>
<rect x="15.0971" y="20.8375" width="2.58122" height="14.06" stroke="url(#paint1_linear_59_17574)" stroke-width="0.367461"/>
</g>
<rect x="14.9133" y="34.7537" width="0.327631" height="0.327897" fill="#03DFFF"/>
<rect x="17.5345" y="34.7537" width="0.327631" height="0.327897" fill="#03DFFF"/>
<g filter="url(#filter5_d_59_17574)">
<rect x="20.8115" y="27.8679" width="2.94868" height="4.59055" fill="url(#paint2_linear_59_17574)"/>
<rect x="20.9953" y="28.0517" width="2.58122" height="4.22309" stroke="url(#paint3_linear_59_17574)" stroke-width="0.367461"/>
</g>
<rect x="20.8115" y="32.1301" width="0.327631" height="0.327897" fill="#03DFFF"/>
<rect x="23.4327" y="32.1301" width="0.327631" height="0.327897" fill="#03DFFF"/>
<rect width="456" height="260" transform="translate(10 40)" fill="url(#paint4_linear_59_17574)" fill-opacity="0.5"/>
<defs>
<filter id="filter0_d_59_17574" x="16.513" y="31.1074" width="218.282" height="1.71856" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.367461" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_59_17574"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0156863 0 0 0 0 0.0156863 0 0 0 0 0.0156863 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17574"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17574" result="shape"/>
</filter>
<filter id="filter1_f_59_17574" x="230.81" y="14.4185" width="37.048" height="34.1121" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.59326" result="effect1_foregroundBlur_59_17574"/>
</filter>
<filter id="filter2_d_59_17574" x="30.6007" y="23.8933" width="194.365" height="5.3255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.367461" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_59_17574"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0470588 0 0 0 0 0.027451 0 0 0 0 0.0156863 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17574"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17574" result="shape"/>
</filter>
<filter id="filter3_f_59_17574" x="0.44602" y="9.13303" width="31.8855" height="40.4212" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.77699" result="effect1_foregroundBlur_59_17574"/>
</filter>
<filter id="filter4_d_59_17574" x="14.5459" y="20.2863" width="3.68365" height="15.1624" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.367461" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_59_17574"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17574"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17574" result="shape"/>
</filter>
<filter id="filter5_d_59_17574" x="20.4441" y="27.5005" width="3.68365" height="5.3255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.367461" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_59_17574"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17574"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17574" result="shape"/>
</filter>
<linearGradient id="paint0_linear_59_17574" x1="16.3877" y1="20.6538" x2="16.3877" y2="35.0813" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFB"/>
<stop offset="1" stop-color="#0070FC"/>
</linearGradient>
<linearGradient id="paint1_linear_59_17574" x1="16.3877" y1="20.6538" x2="16.3877" y2="35.0813" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FDFE"/>
<stop offset="0.5409" stop-color="#05FEFF"/>
<stop offset="1" stop-color="#019FFE"/>
</linearGradient>
<linearGradient id="paint2_linear_59_17574" x1="22.2859" y1="27.8679" x2="22.2859" y2="32.4585" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FDFB"/>
<stop offset="1" stop-color="#0070FC"/>
</linearGradient>
<linearGradient id="paint3_linear_59_17574" x1="22.2859" y1="27.8679" x2="22.2859" y2="32.4585" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FDFE"/>
<stop offset="0.5409" stop-color="#05FEFF"/>
<stop offset="1" stop-color="#019FFE"/>
</linearGradient>
<linearGradient id="paint4_linear_59_17574" x1="228" y1="0" x2="228" y2="260" gradientUnits="userSpaceOnUse">
<stop stop-color="#162142" stop-opacity="0"/>
<stop offset="1" stop-color="#162142"/>
</linearGradient>
</defs>
</svg>
