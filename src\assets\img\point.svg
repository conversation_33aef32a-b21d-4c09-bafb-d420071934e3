<svg width="90" height="26" viewBox="0 0 90 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_70_4220)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M44.995 0.786621L47.7223 4.66979L86.2527 4.59119L89.9087 8.10403C89.9676 8.16075 90 8.23244 90 8.3065V25.6458C90 25.8196 89.8256 25.9605 89.6104 25.9605H4.62933L0.09099 21.5862C0.03222 21.5295 0 21.458 0 21.3841V1.10129C0 0.927494 0.17442 0.786621 0.38961 0.786621H44.995Z" fill="url(#paint0_linear_70_4220)" fill-opacity="0.8"/>
</g>
<path d="M47.6072 4.75067L47.6493 4.81067L47.7226 4.81052L86.1961 4.73204L89.8111 8.20544C89.8457 8.23876 89.8593 8.27501 89.8593 8.3065V25.6458C89.8593 25.7153 89.7774 25.8197 89.6104 25.8197H4.68611L0.188654 21.4849L0.188649 21.4849C0.154221 21.4517 0.140731 21.4156 0.140731 21.3841V1.10129C0.140731 1.0318 0.222577 0.927352 0.38961 0.927352H44.9219L47.6072 4.75067Z" stroke="url(#paint1_linear_70_4220)" stroke-width="0.281462"/>
<defs>
<filter id="filter0_i_70_4220" x="0" y="-1.14843" width="90" height="27.1089" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.93505"/>
<feGaussianBlur stdDeviation="1.2314"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_70_4220"/>
</filter>
<linearGradient id="paint0_linear_70_4220" x1="45" y1="-5.90919" x2="48.8306" y2="29.5252" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FCFF"/>
<stop offset="1" stop-color="#052272"/>
</linearGradient>
<linearGradient id="paint1_linear_70_4220" x1="0" y1="0.786621" x2="0" y2="25.9605" gradientUnits="userSpaceOnUse">
<stop stop-color="#009EDC" stop-opacity="0.01"/>
<stop offset="1" stop-color="#008EFF"/>
</linearGradient>
</defs>
</svg>
