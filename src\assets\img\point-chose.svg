<svg width="90" height="92" viewBox="0 0 90 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M36.165 44.8093C36.165 49.9658 41.9837 58.7907 44.6697 61.4103C46.2005 61.0701 48.2416 59.029 48.2416 59.029C48.2416 59.029 54.8752 53.4159 56.0658 45.7618C56.5761 41.3394 54.7051 37.2572 48.2416 36.4067C41.4379 35.7264 36.165 40.0759 36.165 44.8093ZM43.6727 44.1291C43.6727 42.439 45.0334 41.0674 46.7101 41.0674C48.3867 41.0674 49.7474 42.439 49.7474 44.1291C49.7474 45.8191 48.3867 47.1907 46.7101 47.1907C45.0334 47.1907 43.6727 45.8191 43.6727 44.1291Z" fill="url(#paint0_linear_70_4346)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M36.1663 45.4894C36.1663 51.9189 44.6709 61.41 44.6709 61.41C44.6709 61.41 53.1755 51.9189 53.1755 45.4894C53.1754 40.7561 49.3666 36.9167 44.6709 36.9167C39.9751 36.9167 36.1663 40.7561 36.1663 45.4894ZM41.6331 45.4893C41.6331 43.7993 42.9938 42.4276 44.6705 42.4276C46.3471 42.4276 47.7078 43.7993 47.7078 45.4893C47.7078 47.1794 46.3471 48.551 44.6705 48.551C42.9938 48.551 41.6331 47.1794 41.6331 45.4893Z" fill="url(#paint1_linear_70_4346)"/>
<ellipse cx="44.7309" cy="69.5281" rx="6.95962" ry="3.21213" fill="#FF9839" fill-opacity="0.82"/>
<path d="M45.2665 78.3813C49.6499 78.3813 53.6587 77.5253 56.6024 76.1042C59.4882 74.7111 61.6148 72.6279 61.6148 70.0633C61.6148 67.4987 59.4882 65.4156 56.6024 64.0224C53.6587 62.6013 49.6499 61.7454 45.2665 61.7454C40.8832 61.7454 36.8743 62.6013 33.9307 64.0224C31.0448 65.4156 28.9182 67.4987 28.9182 70.0633C28.9182 72.6279 31.0448 74.7111 33.9307 76.1042C36.8743 77.5253 40.8832 78.3813 45.2665 78.3813Z" stroke="url(#paint2_linear_70_4346)" stroke-width="1.64602"/>
<path d="M44.999 84.8055C52.7027 84.8055 59.717 83.3823 64.8362 81.0464C69.8822 78.7438 73.3928 75.3953 73.3928 71.4017C73.3928 67.408 69.8822 64.0595 64.8362 61.7569C59.717 59.421 52.7027 57.9978 44.999 57.9978C37.2953 57.9978 30.2811 59.421 25.1619 61.7569C20.1158 64.0595 16.6052 67.408 16.6052 71.4017C16.6052 75.3953 20.1158 78.7438 25.1619 81.0464C30.2811 83.3823 37.2953 84.8055 44.999 84.8055Z" stroke="url(#paint3_linear_70_4346)" stroke-width="1.64602"/>
<g filter="url(#filter0_i_70_4346)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M44.995 1.78662L47.7223 5.66979L86.2527 5.59119L89.9087 9.10403C89.9676 9.16075 90 9.23244 90 9.3065V26.6458C90 26.8196 89.8256 26.9605 89.6104 26.9605H4.62933L0.09099 22.5862C0.03222 22.5295 0 22.458 0 22.3841V2.10129C0 1.92749 0.17442 1.78662 0.38961 1.78662H44.995Z" fill="url(#paint4_linear_70_4346)" fill-opacity="0.8"/>
</g>
<path d="M47.6072 5.75067L47.6493 5.81067L47.7226 5.81052L86.1961 5.73204L89.8111 9.20544C89.8457 9.23876 89.8593 9.27501 89.8593 9.3065V26.6458C89.8593 26.7153 89.7774 26.8197 89.6104 26.8197H4.68611L0.188654 22.4849L0.188649 22.4849C0.154221 22.4517 0.140731 22.4156 0.140731 22.3841V2.10129C0.140731 2.0318 0.222577 1.92735 0.38961 1.92735H44.9219L47.6072 5.75067Z" stroke="url(#paint5_linear_70_4346)" stroke-width="0.281462"/>
<g filter="url(#filter1_i_70_4346)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M90 8.08026V5.72021H87.5L90 8.08026Z" fill="#008EFF"/>
</g>
<g filter="url(#filter2_i_70_4346)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.000271082 24.5513L0.000271082 27H2.5293L0.000271082 24.5513Z" fill="#008EFF"/>
</g>
<path d="M0 1H44.9389" stroke="url(#paint6_linear_70_4346)" stroke-width="0.281462"/>
<path d="M48.4407 4.75293H89.8778" stroke="url(#paint7_linear_70_4346)" stroke-width="0.281462"/>
<defs>
<filter id="filter0_i_70_4346" x="0" y="-0.14843" width="90" height="27.1089" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.93505"/>
<feGaussianBlur stdDeviation="1.2314"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.607843 0 0 0 0 1 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_70_4346"/>
</filter>
<filter id="filter1_i_70_4346" x="87.5" y="5.72021" width="2.5" height="2.36011" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.2314"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_70_4346"/>
</filter>
<filter id="filter2_i_70_4346" x="0.000244141" y="24.5513" width="2.52905" height="2.44873" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.2314"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_70_4346"/>
</filter>
<linearGradient id="paint0_linear_70_4346" x1="55.5427" y1="28.2151" x2="34.1595" y2="37.5876" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE500"/>
<stop offset="0.550311" stop-color="#F45800"/>
<stop offset="1" stop-color="#AC5300"/>
</linearGradient>
<linearGradient id="paint1_linear_70_4346" x1="32.1007" y1="42.7877" x2="41.3196" y2="62.9429" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF4D9"/>
<stop offset="0.697345" stop-color="#FF7A00"/>
<stop offset="1" stop-color="#FF5C00"/>
</linearGradient>
<linearGradient id="paint2_linear_70_4346" x1="45.2665" y1="62.5684" x2="45.2665" y2="77.5583" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF5C00" stop-opacity="0"/>
<stop offset="1" stop-color="#FF9900"/>
</linearGradient>
<linearGradient id="paint3_linear_70_4346" x1="44.999" y1="58.8208" x2="44.999" y2="83.9825" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF5C00" stop-opacity="0"/>
<stop offset="1" stop-color="#FF9900"/>
</linearGradient>
<linearGradient id="paint4_linear_70_4346" x1="45" y1="-4.90919" x2="48.8306" y2="30.5252" gradientUnits="userSpaceOnUse">
<stop stop-color="#00FCFF"/>
<stop offset="1" stop-color="#052272"/>
</linearGradient>
<linearGradient id="paint5_linear_70_4346" x1="0" y1="1.78662" x2="0" y2="26.9605" gradientUnits="userSpaceOnUse">
<stop stop-color="#009EDC" stop-opacity="0.01"/>
<stop offset="1" stop-color="#008EFF"/>
</linearGradient>
<linearGradient id="paint6_linear_70_4346" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint7_linear_70_4346" x1="-nan" y1="-nan" x2="-nan" y2="-nan" gradientUnits="userSpaceOnUse">
<stop stop-color="#008CFF" stop-opacity="0.01"/>
<stop offset="0.509159" stop-color="#4AA5FF"/>
<stop offset="1" stop-color="#0074FF" stop-opacity="0.01"/>
</linearGradient>
</defs>
</svg>
