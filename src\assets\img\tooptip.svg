<svg width="382" height="217" viewBox="0 0 382 217" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M371.815 9.28485V205.715H10.7352V9.28485H371.815Z" fill="url(#paint0_radial_59_17389)" stroke="#003F82" stroke-width="0.956916"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.294 8.12573V11.529H9.53198V8.12573H372.294Z" fill="url(#paint1_linear_59_17389)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.294 8.8064V10.8483H9.5321V8.8064H372.294Z" fill="url(#paint2_linear_59_17389)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.294 9.48682V10.1675H9.5321V9.48682H372.294Z" fill="url(#paint3_linear_59_17389)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.294 202.79V206.193H9.5321V202.79H372.294Z" fill="url(#paint4_linear_59_17389)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.294 203.471V205.513H9.53223V203.471H372.294Z" fill="url(#paint5_linear_59_17389)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M372.294 204.151V204.832H9.53223V204.151H372.294Z" fill="url(#paint6_linear_59_17389)"/>
<g filter="url(#filter0_d_59_17389)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M379.548 2V29.2258H378.097V3.36129H350.527V2H379.548Z" fill="#0CD2EB"/>
</g>
<g filter="url(#filter1_d_59_17389)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.00006 2L3.00006 29.2258H4.45111L4.45111 3.36129L32.021 3.36129V2L3.00006 2Z" fill="#0CD2EB"/>
</g>
<g filter="url(#filter2_d_59_17389)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M379.548 213V185.774H378.097V211.639H350.527V213H379.548Z" fill="#0CD2EB"/>
</g>
<g filter="url(#filter3_d_59_17389)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.00006 213L3.00006 185.774H4.45111L4.45111 211.639H32.021V213H3.00006Z" fill="#0CD2EB"/>
</g>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 40.1157V41.477H10.2554V40.1157H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 25.1418V26.5031H10.2554V25.1418H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M26.2172 204.151L26.2172 10.8479H24.7661L24.7661 204.151H26.2172Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M42.1791 204.151L42.1791 10.8479H40.728L40.728 204.151H42.1791Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M58.1399 204.151L58.1399 10.8479H56.6888L56.6888 204.151H58.1399Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M74.1008 204.151L74.1008 10.8479H72.6498L72.6498 204.151H74.1008Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M90.0627 204.151L90.0627 10.8479H88.6117L88.6117 204.151H90.0627Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M106.025 204.151L106.025 10.8479H104.574L104.574 204.151H106.025Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M121.987 204.151L121.987 10.8479H120.536L120.536 204.151H121.987Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M137.948 204.151L137.948 10.8479H136.496L136.496 204.151H137.948Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M153.909 204.151L153.909 10.8479H152.458L152.458 204.151H153.909Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M169.87 204.151L169.87 10.8479H168.419L168.419 204.151H169.87Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M185.832 204.151L185.832 10.8479H184.381L184.381 204.151H185.832Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M201.794 204.151L201.794 10.8479H200.343L200.343 204.151H201.794Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M217.755 204.151L217.755 10.8479H216.304L216.304 204.151H217.755Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M233.717 204.151L233.717 10.8479H232.266L232.266 204.151H233.717Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M249.678 204.151L249.678 10.8479H248.227L248.227 204.151H249.678Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M265.64 204.151V10.8479H264.189V204.151H265.64Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M281.601 204.151V10.8479H280.15V204.151H281.601Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M297.562 204.151V10.8479H296.111V204.151H297.562Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M313.525 204.151V10.8479H312.073V204.151H313.525Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M329.485 204.151V10.8479H328.034V204.151H329.485Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M345.447 204.151V10.8479H343.996V204.151H345.447Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M361.408 204.151V10.8479H359.957V204.151H361.408Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 55.0901V56.4514H10.2554V55.0901H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 70.0642V71.4255H10.2554V70.0642H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 85.0386V86.3999H10.2554V85.0386H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 100.012V101.374H10.2554V100.012H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 114.987V116.348H10.2554V114.987H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 129.961V131.322H10.2554V129.961H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 144.935V146.296H10.2554V144.935H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 159.909V161.271H10.2554V159.909H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 174.883V176.245H10.2554V174.883H370.84Z" fill="#003F82"/>
<path opacity="0.2" fill-rule="evenodd" clip-rule="evenodd" d="M370.84 189.858V191.219H10.2554V189.858H370.84Z" fill="#003F82"/>
<defs>
<filter id="filter0_d_59_17389" x="348.134" y="0.564626" width="33.8056" height="32.0104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.956916"/>
<feGaussianBlur stdDeviation="1.19615"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.415686 0 0 0 0 0.890196 0 0 0 0 0.988235 0 0 0 0.576471 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17389"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17389" result="shape"/>
</filter>
<filter id="filter1_d_59_17389" x="0.60771" y="0.564626" width="33.8056" height="32.0104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.956916"/>
<feGaussianBlur stdDeviation="1.19615"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.415686 0 0 0 0 0.890196 0 0 0 0 0.988235 0 0 0 0.576471 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17389"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17389" result="shape"/>
</filter>
<filter id="filter2_d_59_17389" x="348.134" y="184.339" width="33.8056" height="32.0104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.956916"/>
<feGaussianBlur stdDeviation="1.19615"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.415686 0 0 0 0 0.890196 0 0 0 0 0.988235 0 0 0 0.576471 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17389"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17389" result="shape"/>
</filter>
<filter id="filter3_d_59_17389" x="0.60771" y="184.339" width="33.8056" height="32.0104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.956916"/>
<feGaussianBlur stdDeviation="1.19615"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.415686 0 0 0 0 0.890196 0 0 0 0 0.988235 0 0 0 0.576471 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_59_17389"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_59_17389" result="shape"/>
</filter>
<radialGradient id="paint0_radial_59_17389" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(191.275 107.5) rotate(90) scale(163.905 300.625)">
<stop stop-color="#133EAD"/>
<stop offset="1" stop-color="#051220" stop-opacity="0.63"/>
</radialGradient>
<linearGradient id="paint1_linear_59_17389" x1="9.53198" y1="11.529" x2="372.294" y2="11.529" gradientUnits="userSpaceOnUse">
<stop stop-color="#0E2163" stop-opacity="0.54902"/>
<stop offset="0.410104" stop-color="#0093FF"/>
<stop offset="0.48523" stop-color="#0084EE"/>
<stop offset="0.53862" stop-color="#007DF2"/>
<stop offset="1" stop-color="#00336F"/>
</linearGradient>
<linearGradient id="paint2_linear_59_17389" x1="9.5321" y1="10.843" x2="372.294" y2="10.843" gradientUnits="userSpaceOnUse">
<stop stop-color="#000E40"/>
<stop offset="0.410104" stop-color="#0093FF"/>
<stop offset="0.48523" stop-color="#86FFFF"/>
<stop offset="0.53862" stop-color="#007DF2"/>
<stop offset="1" stop-color="#003774"/>
</linearGradient>
<linearGradient id="paint3_linear_59_17389" x1="9.5321" y1="9.85373" x2="372.294" y2="9.85373" gradientUnits="userSpaceOnUse">
<stop stop-color="#407DC4" stop-opacity="0.278431"/>
<stop offset="0.410104" stop-color="#00FFFB"/>
<stop offset="0.494151" stop-color="white"/>
<stop offset="0.629046" stop-color="#00DDFF"/>
<stop offset="1" stop-color="#1F738A" stop-opacity="0.196078"/>
</linearGradient>
<linearGradient id="paint4_linear_59_17389" x1="9.5321" y1="206.193" x2="372.294" y2="206.193" gradientUnits="userSpaceOnUse">
<stop stop-color="#0E2163" stop-opacity="0.54902"/>
<stop offset="0.410104" stop-color="#0093FF"/>
<stop offset="0.48523" stop-color="#0084EE"/>
<stop offset="0.53862" stop-color="#007DF2"/>
<stop offset="1" stop-color="#00336F"/>
</linearGradient>
<linearGradient id="paint5_linear_59_17389" x1="9.53223" y1="205.507" x2="372.294" y2="205.507" gradientUnits="userSpaceOnUse">
<stop stop-color="#000E40"/>
<stop offset="0.410104" stop-color="#0093FF"/>
<stop offset="0.48523" stop-color="#86FFFF"/>
<stop offset="0.53862" stop-color="#007DF2"/>
<stop offset="1" stop-color="#003774"/>
</linearGradient>
<linearGradient id="paint6_linear_59_17389" x1="9.53223" y1="204.518" x2="372.294" y2="204.518" gradientUnits="userSpaceOnUse">
<stop stop-color="#407DC4" stop-opacity="0.278431"/>
<stop offset="0.410104" stop-color="#00FFFB"/>
<stop offset="0.494151" stop-color="white"/>
<stop offset="0.629046" stop-color="#00DDFF"/>
<stop offset="1" stop-color="#1F738A" stop-opacity="0.196078"/>
</linearGradient>
</defs>
</svg>
