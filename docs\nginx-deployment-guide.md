# Nginx 部署配置指南

## 概述

本文档说明如何配置 Nginx 来支持人员资质预览页面的 `/preview` 前缀代理。

## 配置文件

项目根目录下的 `nginx_preview_proxy.conf` 文件包含了完整的 Nginx 配置。

## 部署步骤

### 1. 修改配置文件

根据您的实际环境修改 `nginx_preview_proxy.conf` 中的以下配置：

```nginx
upstream backend_api {
    # 修改为您的后端 API 服务器地址
    server 127.0.0.1:8080;  # 示例：本地 8080 端口
    # server **********:8080;  # 示例：远程服务器
}

server {
    listen 80;
    server_name localhost;  # 修改为您的域名
    
    # 修改为您的前端静态资源目录
    root /usr/share/nginx/html;
    
    # ... 其他配置
}
```

### 2. 关键配置说明

#### 2.1 `/preview` 前缀代理

```nginx
location /preview/ {
    # 代理到后端服务的 /preview/ 路径
    proxy_pass http://backend_api/preview/;
    
    # 必要的代理头设置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

#### 2.2 CORS 配置

配置文件包含了完整的 CORS 支持，包括：
- 允许所有来源的跨域请求
- 支持常用的 HTTP 方法
- 包含必要的请求头（如 `token`, `LongToken`）
- 处理预检请求（OPTIONS）

#### 2.3 静态资源配置

```nginx
location / {
    try_files $uri $uri/ /index.html;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # HTML 文件不缓存
    location ~* \.html$ {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
}
```

### 3. 部署配置文件

#### 3.1 复制配置文件

```bash
# 复制配置文件到 Nginx 配置目录
sudo cp nginx_preview_proxy.conf /etc/nginx/sites-available/preview-app

# 创建软链接启用站点
sudo ln -s /etc/nginx/sites-available/preview-app /etc/nginx/sites-enabled/

# 或者直接替换默认配置
sudo cp nginx_preview_proxy.conf /etc/nginx/nginx.conf
```

#### 3.2 测试配置

```bash
# 测试 Nginx 配置语法
sudo nginx -t

# 如果测试通过，重新加载配置
sudo nginx -s reload

# 或者重启 Nginx 服务
sudo systemctl restart nginx
```

### 4. 验证部署

#### 4.1 检查服务状态

```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep :80
```

#### 4.2 测试 API 代理

```bash
# 测试 /preview 接口代理
curl -X POST http://your-domain/preview/distribute/queryzizhi \
  -H "Content-Type: application/json" \
  -d '{"biaodanshiliid": "test-id"}'

# 测试 CORS 预检请求
curl -X OPTIONS http://your-domain/preview/distribute/queryzizhi \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type"
```

#### 4.3 测试前端页面

访问以下 URL 测试页面是否正常加载：
```
http://your-domain/personnel-qualification-preview?biaodanshiliid=test-id
```

## 常见问题

### 1. 502 Bad Gateway 错误

**原因：** 后端服务不可用或配置错误

**解决方案：**
- 检查后端服务是否正常运行
- 验证 `upstream backend_api` 中的服务器地址是否正确
- 检查防火墙设置

### 2. CORS 错误

**原因：** 跨域配置不正确

**解决方案：**
- 确保 CORS 头配置正确
- 检查是否正确处理 OPTIONS 预检请求
- 验证 `Access-Control-Allow-Headers` 包含所需的头部

### 3. 静态资源 404 错误

**原因：** 静态资源路径配置错误

**解决方案：**
- 检查 `root` 指令指向的目录是否正确
- 确保静态资源文件已正确部署到指定目录
- 验证文件权限设置

## 安全建议

1. **限制 CORS 来源**：在生产环境中，将 `Access-Control-Allow-Origin "*"` 改为具体的域名
2. **启用 HTTPS**：使用 SSL/TLS 加密传输
3. **设置适当的缓存策略**：避免敏感数据被缓存
4. **配置访问日志**：便于监控和调试

## 监控和日志

配置文件中已包含日志配置：
- 访问日志：`/var/log/nginx/preview_access.log`
- 错误日志：`/var/log/nginx/preview_error.log`

可以通过以下命令查看日志：
```bash
# 查看访问日志
sudo tail -f /var/log/nginx/preview_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/preview_error.log
```
