//路由鉴权:鉴权,项目当中路由能不能被的权限的设置(某一个路由什么条件下可以访问、什么条件下不可以访问)
// import setting from './setting'

import router from "@/router";
//@ts-ignore
import nprogress from "nprogress";
//引入进度条样式
import "nprogress/nprogress.css";

nprogress.configure({ showSpinner: false });
// 获取用户相关的小仓库内部token数据, 去判断用户是否登录成功
// 全局守卫:项目当中任意路由切换都会触发的钩子
// 全局前置守卫

router.beforeEach(async (to: any, _: any, next: any) => {
  // 获取浏览器地址

  nprogress.start();
  //获取token,去判断用户登录、还是未登录

  if (to.path == "/login") {
    next({ path: "/" });
  } else {
    //有用户信息
    next();
  }
});
//全局后置守卫
router.afterEach(() => {
  nprogress.done();
});
// #endregion
