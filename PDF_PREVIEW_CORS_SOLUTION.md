# PDF预览CORS跨域问题解决方案

## 🎯 问题背景

**原始问题**：
- PDF文件服务器：`http://************:8090`
- 前端应用：`http://localhost:8853`
- CORS错误：`Access-Control-Allow-Origin` header缺失
- 无法直接预览跨域PDF文件

**解决思路**：
通过后端API获取文件的ArrayBuffer数据，转换为Blob URL，避免直接跨域访问文件服务器。

## 🔧 实现方案

### 1. 创建文件预览API模块

**文件位置**：`src/utils/request/filePreview.ts`

#### 核心功能

1. **URL路径提取**
```typescript
export function extractS3Path(fullUrl: string): string {
  // 从完整URL中提取S3路径
  // 输入: http://************:8090/object/3gZabmbEVL/s3/tanlucloud-conf/files/2025-08/file.pdf
  // 输出: /tanlucloud-conf/files/2025-08/file.pdf
}
```

2. **文件预览数据获取**
```typescript
export async function getFilePreviewBuffer(s3Path: string): Promise<ArrayBuffer> {
  // 通过后端API获取文件的ArrayBuffer数据
  const response = await request.request({
    url: '/file/preview',
    method: 'POST',
    data: { filePath: s3Path },
    responseType: 'arraybuffer'
  });
  return response.data;
}
```

3. **Blob URL生成**
```typescript
export async function getFilePreviewBlobUrl(s3Path: string, mimeType: string = 'application/pdf'): Promise<string> {
  const arrayBuffer = await getFilePreviewBuffer(s3Path);
  const blob = new Blob([arrayBuffer], { type: mimeType });
  return URL.createObjectURL(blob);
}
```

4. **文件预览管理器**
```typescript
export class FilePreviewManager {
  // 管理Blob URL的生命周期
  // 自动清理避免内存泄漏
}
```

### 2. 修改预览组件

**文件位置**：`src/views/personnelQualificationPreview/index.vue`

#### 主要修改

1. **导入新模块**
```typescript
import { 
  filePreviewManager, 
  needsApiPreview, 
  extractS3Path 
} from "@/utils/request/filePreview";
```

2. **新增响应式变量**
```typescript
// PDF预览URL缓存
const pdfPreviewUrl = ref<string>("");
const isLoadingPdfPreview = ref(false);
const currentPdfUrl = ref<string>("");
```

3. **PDF预览URL生成函数**
```typescript
const getPdfPreviewUrl = async (url: string): Promise<string> => {
  if (needsApiPreview(url)) {
    // 使用API预览
    return await filePreviewManager.createPreviewUrl(url, 'application/pdf');
  } else {
    // 使用原有逻辑
    return getFullUrl(url);
  }
};
```

4. **响应式URL更新**
```typescript
watch(displayAttachment, async (newAttachment: ZizhiFujian | null) => {
  if (newAttachment && isPdf(newAttachment.url)) {
    currentPdfUrl.value = await getPdfPreviewUrl(newAttachment.url);
  }
}, { immediate: true });
```

5. **模板更新**
```vue
<!-- PDF预览 -->
<div v-if="useTestPdf || (displayAttachment && isPdf(displayAttachment.url))" class="pdf-preview">
  <!-- 加载状态 -->
  <div v-if="isLoadingPdfPreview" class="pdf-loading">
    <el-icon class="is-loading"><Loading /></el-icon>
    <span>正在加载PDF预览...</span>
  </div>
  
  <!-- PDF预览组件 -->
  <vue-office-pdf
    v-else
    :src="currentPdfUrl"
    style="width: 100%; height: 100%"
    @rendered="onPdfRendered"
    @error="onPdfError"
  />
</div>
```

6. **资源清理**
```typescript
onUnmounted(() => {
  cleanup();
  filePreviewManager.cleanup(); // 清理Blob URL
});
```

## 📊 数据流程

### 原始流程（有CORS问题）
```
前端 → 直接访问文件服务器 → CORS错误
http://localhost:8853 → http://************:8090/object/.../file.pdf ❌
```

### 新流程（解决CORS）
```
前端 → 后端API → 文件服务器 → 返回ArrayBuffer → 生成Blob URL → 预览
http://localhost:8853 → /file/preview → ************:8090 → ArrayBuffer → blob:// ✅
```

## 🔍 URL处理逻辑

### URL转换示例

**输入URL**：
```
http://************:8090/object/3gZabmbEVL/s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
```

**处理步骤**：
1. 检测到跨域URL：`needsApiPreview(url) = true`
2. 提取S3路径：`extractS3Path(url) = "/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf"`
3. 调用API：`POST /file/preview { filePath: "/tanlucloud-conf/files/2025-08/..." }`
4. 获取ArrayBuffer数据
5. 生成Blob URL：`blob:http://localhost:8853/uuid-string`
6. 用于PDF预览

## 🎨 用户体验改进

### 加载状态
- 显示加载动画和提示文字
- 防止用户在加载过程中的困惑

### 错误处理
- API调用失败时fallback到原URL
- 显示友好的错误提示

### 资源管理
- 自动清理Blob URL避免内存泄漏
- 组件卸载时释放所有资源

## 🔧 后端接口要求

### 接口规范
```
POST /file/preview
Content-Type: application/json

Request Body:
{
  "filePath": "/tanlucloud-conf/files/2025-08/filename.pdf"
}

Response:
- Content-Type: application/pdf (或对应的MIME类型)
- Body: 文件的二进制数据 (ArrayBuffer)
```

### 实现要点
1. 接收S3路径参数
2. 从文件服务器获取文件数据
3. 直接返回二进制数据
4. 设置正确的Content-Type

## ✅ 向后兼容性

### 兼容性保证
1. **本地文件**：继续使用原有逻辑
2. **同域文件**：不经过API，直接访问
3. **跨域文件**：自动使用新的API预览
4. **错误降级**：API失败时fallback到原URL

### 检测逻辑
```typescript
function needsApiPreview(url: string): boolean {
  const crossOriginPatterns = [
    /^https?:\/\/172\.20\.18\.72:8090/,
    /\/s3\//,
    /\/object\//
  ];
  return crossOriginPatterns.some(pattern => pattern.test(url));
}
```

## 🧪 测试场景

### 1. 跨域PDF文件
- URL包含`************:8090`
- 应该通过API预览
- 显示加载状态

### 2. 本地PDF文件
- URL为相对路径或同域
- 应该直接预览
- 不显示加载状态

### 3. 测试PDF文件
- `useTestPdf = true`
- 使用`/pdfTest.pdf`
- 正常预览

### 4. 错误处理
- API调用失败
- 应该fallback到原URL
- 显示错误信息

## 🎯 预期效果

修改完成后：
1. ✅ 跨域PDF文件可以正常预览
2. ✅ 保持原有的多附件切换功能
3. ✅ 图片预览和文件下载功能不受影响
4. ✅ 显示友好的加载状态
5. ✅ 自动资源清理，无内存泄漏
6. ✅ 完全向后兼容

现在PDF预览功能已经完全支持跨域文件访问，解决了CORS问题！
