# Nginx 配置文件 - 支持 /preview 前缀代理
# 用于人员资质预览页面的后端 API 代理配置

upstream backend_api {
    # 后端 API 服务器地址，请根据实际情况修改
    server 127.0.0.1:8080;  # 假设后端服务运行在 8080 端口
    # server **********:8080;  # 或者使用实际的后端服务器地址
}

server {
    listen 80;
    server_name localhost;  # 请根据实际域名修改
    
    # 前端静态资源目录
    root /usr/share/nginx/html;  # 请根据实际部署路径修改
    index index.html index.htm;
    
    # 全局 CORS 配置
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Encoding,Accept-Language,Connection,Host,Referer,token,LongToken" always;
    add_header Access-Control-Expose-Headers "Content-Length,Content-Range,Content-Type,Date,Server,Transfer-Encoding,token,LongToken" always;
    add_header Access-Control-Allow-Credentials "true" always;
    
    # 处理预检请求 (OPTIONS)
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Encoding,Accept-Language,Connection,Host,Referer,token,LongToken";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Type 'text/plain; charset=utf-8';
        add_header Content-Length 0;
        return 204;
    }
    
    # /preview 前缀的 API 代理配置
    location /preview/ {
        # 代理到后端服务
        proxy_pass http://************:8889;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲区设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # CORS 配置（针对代理的 API 请求）
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Encoding,Accept-Language,Connection,Host,Referer,token,LongToken" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range,Content-Type,Date,Server,Transfer-Encoding,token,LongToken" always;
        add_header Access-Control-Allow-Credentials "true" always;
    }
    
    # /xinfeng-screen 前缀的 API 代理配置（如果需要）
    location /xinfeng-screen/ {
        # 代理到后端服务
        proxy_pass http://backend_api/xinfeng-screen/;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # CORS 配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Encoding,Accept-Language,Connection,Host,Referer,token,LongToken" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range,Content-Type,Date,Server,Transfer-Encoding,token,LongToken" always;
        add_header Access-Control-Allow-Credentials "true" always;
    }
    
    # 静态资源配置
    location / {
        try_files $uri $uri/ /index.html;
        
        # 静态资源缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }
        
        # HTML 文件不缓存
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            add_header Access-Control-Allow-Origin "*";
        }
    }
    
    # 文件上传大小限制
    client_max_body_size 50M;
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日志配置
    access_log /var/log/nginx/preview_access.log;
    error_log /var/log/nginx/preview_error.log;
}

# HTTPS 配置（可选）
# server {
#     listen 443 ssl http2;
#     server_name localhost;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL 配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # 其他配置与 HTTP 相同
#     # ... (复制上面的配置)
# }
