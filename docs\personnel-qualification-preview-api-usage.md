# 人员资质预览页面 API 使用说明

## 概述

人员资质预览页面已经修改为从后端接口获取数据，而不是从静态的 `testData` 属性获取。

## 修改内容

### 1. 数据获取方式变更

**原来的方式：**
- 从路由参数中获取 `data` 属性作为数据源
- 使用静态的 `testData` 数据

**新的方式：**
- 从 URL 查询参数中获取 `biaodanshiliid` 参数
- 调用后端接口 `/preview/distribute/queryzizhi` 获取数据

### 2. 接口详情

- **URL：** `/preview/distribute/queryzizhi`
- **方法：** POST
- **请求参数：** 
  ```json
  {
    "biaodanshiliid": "从URL查询参数获取的值"
  }
  ```
- **响应格式：**
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "items": [
        {
          "shoujihaoma": "13323096576",
          "xingming": "测试0810-003",
          "shenheleixing": "1",
          "zizhi_items": [
            {
              "zizhishixiaoriqi": "2025-08-11 00:00:00",
              "zizhifujian": "{\"filename\":\"测试.png\",\"size\":5234,\"name\":\"测试.png\",\"mimetype\":\"image/png\",\"state\":\"uploaded\",\"id\":\"9543c665ac34\",\"value\":\"/object/68g75PRdRj/s3/tanlucloud-conf/files/2025-08/c78dda64bf81d5bb1961960df7d9d2b7.png\",\"url\":\"/object/68g75PRdRj/s3/tanlucloud-conf/files/2025-08/c78dda64bf81d5bb1961960df7d9d2b7.png\"}",
              "zizhileixing": "1",
              "zizhishengxiaoriqi": "2025-08-10 00:00:00",
              "zizhizhuangtai": "1"
            }
          ],
          "guishubumen": "安健环管理部",
          "shenhezhuangtai": "3",
          "renyuanxingming": "测试0810-003",
          "gongzhong": "1",
          "guishubanzu": null,
          "biaodanshiliid": "1efc2129-b909-438c-a2f7-328bfea9",
          "chengbaoshangmingcheng": "测试企业0810-001",
          "zhuanye": "1"
        }
      ]
    }
  }
  ```

### 3. 使用方式

访问页面时需要在 URL 中提供 `biaodanshiliid` 查询参数：

```
/personnel-qualification-preview?biaodanshiliid=1efc2129-b909-438c-a2f7-328bfea9
```

### 4. 功能特性

1. **加载状态显示：** 在数据加载过程中显示加载指示器
2. **错误处理：** 如果 API 调用失败，会尝试从 sessionStorage 加载备份数据
3. **向后兼容：** 仍然支持原有的数据传输方式作为备选方案
4. **数据格式转换：** 自动将接口返回的数据格式转换为组件所需的格式

### 5. 数据处理逻辑

1. 优先从 URL 查询参数获取 `biaodanshiliid`
2. 如果存在 `biaodanshiliid`，调用 API 获取数据
3. 如果 API 调用失败，尝试从 sessionStorage 加载备份数据
4. 如果以上都失败，回退到原有的路由参数数据传输方式

### 6. 技术实现

- 使用 `src/utils/request/index.ts` 中的请求工具
- 添加了 `isLoadingData` 状态管理加载状态
- 实现了 `fetchPersonnelQualificationData` 函数处理 API 调用
- 添加了 `parseZizhiFujian` 函数解析资质附件数据
- 修改了 `initializeData` 函数支持异步数据获取

## 测试建议

1. 确保后端接口 `/preview/distribute/queryzizhi` 可用
2. 使用有效的 `biaodanshiliid` 参数测试页面
3. 测试网络错误情况下的降级处理
4. 验证数据格式转换是否正确
