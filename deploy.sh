#!/bin/bash

# 部署脚本：构建和部署人员资质预览应用
# 支持 /preview 前缀代理的 Nginx 配置

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始部署人员资质预览应用..."
    
    # 检查必要的命令
    log_info "检查依赖..."
    check_command "node"
    check_command "npm"
    
    # 安装依赖
    log_info "安装依赖..."
    if [ -f "pnpm-lock.yaml" ]; then
        check_command "pnpm"
        pnpm install
    elif [ -f "yarn.lock" ]; then
        check_command "yarn"
        yarn install
    else
        npm install
    fi
    
    # 构建项目
    log_info "构建项目..."
    if [ -f "pnpm-lock.yaml" ]; then
        pnpm run build
    elif [ -f "yarn.lock" ]; then
        yarn build
    else
        npm run build
    fi
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist 目录不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
    
    # 选择部署方式
    echo ""
    echo "请选择部署方式："
    echo "1) Docker 部署"
    echo "2) 直接 Nginx 部署"
    echo "3) 仅生成配置文件"
    read -p "请输入选择 (1-3): " choice
    
    case $choice in
        1)
            deploy_docker
            ;;
        2)
            deploy_nginx
            ;;
        3)
            generate_config
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# Docker 部署
deploy_docker() {
    log_info "开始 Docker 部署..."
    
    check_command "docker"
    check_command "docker-compose"
    
    # 创建日志目录
    mkdir -p logs/nginx logs/backend
    
    # 构建和启动服务
    log_info "构建 Docker 镜像..."
    docker-compose build
    
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose ps
    
    log_success "Docker 部署完成"
    log_info "访问地址: http://localhost"
    log_info "查看日志: docker-compose logs -f"
}

# 直接 Nginx 部署
deploy_nginx() {
    log_info "开始 Nginx 部署..."
    
    check_command "nginx"
    
    # 检查 Nginx 配置
    if [ ! -f "nginx_preview_proxy.conf" ]; then
        log_error "nginx_preview_proxy.conf 配置文件不存在"
        exit 1
    fi
    
    # 备份现有配置
    if [ -f "/etc/nginx/sites-enabled/default" ]; then
        log_info "备份现有 Nginx 配置..."
        sudo cp /etc/nginx/sites-enabled/default /etc/nginx/sites-enabled/default.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 复制配置文件
    log_info "复制 Nginx 配置..."
    sudo cp nginx_preview_proxy.conf /etc/nginx/sites-available/preview-app
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/preview-app /etc/nginx/sites-enabled/default
    
    # 复制静态文件
    log_info "复制静态文件..."
    sudo rm -rf /usr/share/nginx/html/*
    sudo cp -r dist/* /usr/share/nginx/html/
    sudo chown -R www-data:www-data /usr/share/nginx/html
    
    # 测试配置
    log_info "测试 Nginx 配置..."
    sudo nginx -t
    
    # 重新加载 Nginx
    log_info "重新加载 Nginx..."
    sudo systemctl reload nginx
    
    log_success "Nginx 部署完成"
    log_info "访问地址: http://localhost"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."
    
    # 提示用户修改配置
    log_warning "请根据您的环境修改以下配置文件："
    echo "1. nginx_preview_proxy.conf - Nginx 配置"
    echo "2. docker-compose.yml - Docker 编排配置"
    echo "3. Dockerfile.nginx - Docker 镜像配置"
    
    log_info "主要需要修改的配置项："
    echo "- 后端服务器地址 (upstream backend_api)"
    echo "- 域名 (server_name)"
    echo "- 静态资源路径 (root)"
    echo "- SSL 证书路径 (如果使用 HTTPS)"
    
    log_success "配置文件已生成完成"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
}

# 捕获退出信号
trap cleanup EXIT

# 执行主函数
main "$@"
