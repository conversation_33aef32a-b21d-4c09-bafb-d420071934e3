import { defineStore } from "pinia";
import { reqChangeTenant, reqLogin, reqLogout, reqUserInfo, checkApplicationLock } from "@/api/user";
import { constantRoute } from "@/router/routes";
import { loginRequestListI, encrypUserInfo } from "@/store/types/type";

export const useUserStore = defineStore(
  "User",
  {
    state: () => {
      return {
        token: "", // token
        longToken: "", // 长token
        username: "", //用户名字验证token有效的重要手段
        menuState: constantRoute, //菜单需要的数据
        loginUserTenantList: [{ tenantId: "", tenantName: "" }], //用户登录的租户列表
        companyName: "", // 企业名称
        selectIdIndex: 0, // 企业ID
        isLock: false,
      };
    },
    actions: {
      async userLogin(
        data: any,
        callback: (resList: loginRequestListI[]) => void
      ) {
        const res = await reqLogin(data);
        this.loginUserTenantList = res.loginUserTenantList;
        callback(res.loginUserTenantList);
      },
      async changeTrent(reqChangeTenantId: string) {
        this.selectIdIndex = this.loginUserTenantList.findIndex((item) => {
          if (item.tenantId === reqChangeTenantId) {
            this.companyName = item.tenantName;
            return true;
          }
        });
        const res = await reqChangeTenant(reqChangeTenantId);
        this.token = res;
        this.longToken = res.longToken
      },
      async userLogout(callback: () => void) {
        if (this.username) {
          await reqLogout();
        }
        this.$reset();
        callback();
      },
      async userResetPasswordLogout(callback: () => void) {
        this.$reset();
        callback();
      },
      async stateReqUserInfo() {
        const result = await reqUserInfo();
        this.username = result.name;
        this.companyName = result.tenantName;
        return this.username;
      },
      getToken() {
        return this.token;
      },
      userSetToken(token: string) {
        this.token = token;
      },
      async userEncrypUserInfo(info: encrypUserInfo) {
        this.username = info.username;
        this.companyName = info.systemName;
        this.token = info.token;
        this.longToken = info.longToken;
        this.loginUserTenantList = info.loginUserTenantList;

        // 找到companyName在loginUserTenantList中的索引
        this.selectIdIndex = this.loginUserTenantList.findIndex((item) => {
          if (item.tenantName === info.systemName) {
            return true;
          }
        });
        const res = await checkApplicationLock({
          systemCode: info.systemCode
        })
        this.isLock = res
      },
    },
    persist: [
      {
        paths: ["token"],
        storage: localStorage,
        key: "TOKEN",
      },
      {
        paths: ['isLock'],
        storage: localStorage,
        key: 'isLock',
      },
      {
        paths: ["longToken"],
        storage: localStorage,
        key: "longToken",
      },
      {
        paths: ["menuState"],
        storage: localStorage,
        key: "MENUSTATE",
      },
      {
        paths: ["id"],
        storage: localStorage,
        key: "ID",
      },
      {
        paths: ["companyName"],
        storage: localStorage,
        key: "COMPANYNAME",
      },
      {
        paths: ["loginUserTenantList"],
        storage: localStorage,
        key: "LOGINUSERTENANTLIST",
      },
      {
        paths: ["selectIdIndex"],
        storage: localStorage,
        key: "SELECTID",
      },
    ],
  }
  // persist: true,
);
export default useUserStore;
