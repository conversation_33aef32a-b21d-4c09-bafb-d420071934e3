<template>
  <div class="countdown-container">
    <div class="circle-background">
      <svg class="progress-ring" width="200" height="200">
        <defs>
          <linearGradient
            id="progressGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            <stop offset="0%" :style="`stop-color: ${progressColor.start}`" />
            <stop offset="100%" :style="`stop-color: ${progressColor.end}`" />
          </linearGradient>
        </defs>
        <circle
          class="progress-ring__circle-bg"
          stroke="#E8EBF0"
          stroke-width="8"
          fill="transparent"
          :r="radius"
          :cx="center"
          :cy="center"
          :stroke-dasharray="circumference"
        />
        <circle
          class="progress-ring__circle"
          stroke="url(#progressGradient)"
          stroke-width="8"
          fill="transparent"
          :r="radius"
          :cx="center"
          :cy="center"
          :stroke-dasharray="circumference"
          :style="{ strokeDashoffset }"
        />
        <circle
          class="progress-ring__dot"
          :cx="getDotPosition.x"
          :cy="getDotPosition.y"
          r="6"
          fill="white"
          stroke="#2B43FF"
          stroke-width="3"
        />
      </svg>
    </div>
    <!-- <svg
      width="364"
      height="364"
      viewBox="0 0 364 364"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style="position: absolute; top: -57px"
    >
      <g filter="url(#filter0_f_1264_19323)">
        <circle cx="182.107" cy="181.785" r="91.6" fill="#B9C5FF" />
      </g>
      <defs>
        <filter
          id="filter0_f_1264_19323"
          x="0.644394"
          y="0.322128"
          width="362.926"
          height="362.926"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="35.2"
            result="effect1_foregroundBlur_1264_19323"
          />
        </filter>
      </defs>
    </svg> -->

    <div class="time-display">
      剩余学习时长
      <div class="date" v-if="showDate && !props.isMinimized">
        {{ formattedDate }}
      </div>
      <div
        class="time"
        :class="{
          'seconds-mode': isSecondsMode,
          'minimized-mode': props.isMinimized,
        }"
      >
        {{ props.isMinimized ? minimizedTime : formattedTime }}
      </div>
      <!-- <div class="total-time" v-if="showTime && !props.isMinimized">
        共计{{ totalTimeText }}
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";

dayjs.extend(duration);

interface Props {
  year?: number;
  month?: number;
  day?: number;
  hour?: number;
  minute?: number;
  second?: number;
  type?: "countdown" | "countup";
  modelValue?: number;
  isPcEnv?: boolean;
  isMinimized?: boolean;
  currentTime?: number;
}
const props = withDefaults(defineProps<Props>(), {
  type: "countdown",
  modelValue: 0,
  isPcEnv: () => true,
  isMinimized: false,
  currentTime: () => 0,
});

const emit = defineEmits(["update:modelValue"]);

const timer = ref<number | null>(null);
const currentTime = ref<number>(0);
const center = 100;
const radius = 85;
const circumference = 2 * Math.PI * radius;

const totalDuration = computed(() => {
  let duration = 0;
  if (props.year) duration += props.year * 365 * 24 * 60 * 60 * 1000;
  if (props.month) duration += props.month * 30 * 24 * 60 * 60 * 1000;
  if (props.day) duration += props.day * 24 * 60 * 60 * 1000;
  if (props.hour) duration += props.hour * 60 * 60 * 1000;
  if (props.minute) duration += props.minute * 60 * 1000;
  if (props.second) duration += props.second * 1000;
  return duration;
});

const remainingTime = computed(() => {
  if (props.type === "countdown") {
    return totalDuration.value - props.currentTime;
  }
  return props.currentTime;
});

const isSecondsMode = computed(() => {
  return remainingTime.value <= 60000 && !props.isMinimized;
});

// 新增：缩小状态下只显示最大单位
const minimizedTime = computed(() => {
  const duration = dayjs.duration(remainingTime.value);

  if (props.year && duration.years() > 0) {
    return `${duration.years()}year`;
  }
  if (props.month && duration.months() > 0) {
    return `${duration.months()}month`;
  }
  if (props.day && duration.days() > 0) {
    return `${duration.days()}day`;
  }
  if (props.hour && duration.hours() > 0) {
    return `${duration.hours()}h`;
  }
  if (props.minute && duration.minutes() > 0) {
    return `${duration.minutes()}min`;
  }
  return `${duration.seconds()}s`;
});

const updateModelValue = () => {
  const currentSeconds = remainingTime.value / 1000;
  if (props.type === "countdown") {
    emit("update:modelValue", currentSeconds);
  } else {
    emit("update:modelValue", totalDuration.value / 1000 - currentSeconds);
  }
};

const progressColor = computed(() => {
  // const seconds = remainingTime.value / 1000;

  // if (seconds < 60) {
  //   return {
  //     start: "#F53F3F",
  //     end: "#FF0000",
  //   };
  // } else if (seconds >= 60 && seconds <= 180) {
  //   return {
  //     start: "#FFD271",
  //     end: "#FFAF02",
  //   };
  // } else {
  //   return {
  //     start: "#8492FF",
  //     end: "#2B43FF",
  //   };
  // }
  return {
    start: "#8492FF",
    end: "#2B43FF",
  };
});

const formattedTime = computed(() => {
  const duration = dayjs.duration(remainingTime.value);

  if (isSecondsMode.value) {
    return `${Math.floor(remainingTime.value / 1000)}s`;
  }

  const parts = [];
  if (props.hour || duration.hours() > 0) {
    parts.push(String(duration.hours()).padStart(2, "0"));
  }
  if (props.minute || parts.length > 0 || duration.minutes() > 0) {
    parts.push(String(duration.minutes()).padStart(2, "0"));
  }
  parts.push(String(duration.seconds()).padStart(2, "0"));

  return parts.join(":");
});

const formattedDate = computed(() => {
  const duration = dayjs.duration(remainingTime.value);
  const parts = [];

  if (props.year) {
    parts.push(`${duration.years()}年`);
  }
  if (props.month) {
    parts.push(`${duration.months()}月`);
  }
  if (props.day) {
    parts.push(`${duration.days()}日`);
  }

  return parts.join("");
});

const totalTimeText = computed(() => {
  const parts = [];
  if (props.year) parts.push(`${props.year}年`);
  if (props.month) parts.push(`${props.month}月`);
  if (props.day) parts.push(`${props.day}日`);
  if (props.hour) parts.push(`${props.hour}时`);
  if (props.minute) parts.push(`${props.minute}分`);
  if (props.second) parts.push(`${props.second}秒`);
  return parts.join("");
});

const strokeDashoffset = computed(() => {
  const progress = remainingTime.value / totalDuration.value;
  return circumference * (1 - progress);
});

const showDate = computed(() => {
  return props.year || props.month || props.day;
});

const showTime = computed(() => {
  return (
    props.year ||
    props.month ||
    props.day ||
    props.hour ||
    props.minute ||
    props.second
  );
});

const getDotPosition = computed(() => {
  const progress = totalDuration.value === 0 ? 0 : remainingTime.value / totalDuration.value;
  const angle = progress * 2 * Math.PI;
  return {
    x: center + radius * Math.cos(angle),
    y: center + radius * Math.sin(angle),
  };
});

const startTimer = () => {
  timer.value = window.setInterval(() => {
    if (props.type === "countdown" && remainingTime.value <= 0) {
      stopTimer();
      return;
    }
    if (
      props.type === "countup" &&
      remainingTime.value >= totalDuration.value
    ) {
      stopTimer();
      return;
    }
    currentTime.value += 1000;
    updateModelValue();
  }, 1000);
};

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

onMounted(() => {
  // updateModelValue();
  // startTimer();
});

onUnmounted(() => {
  // stopTimer();
});

const containerWidth = computed(() => {
  return props.isPcEnv ? "200px" : "100%";
});
</script>

<style scoped>
.countdown-container {
  position: relative;
  width: v-bind(containerWidth);
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, sans-serif;
}

.circle-background {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0px 3.27px 7.35px 0px #00000026;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring__circle {
  transition: stroke-dashoffset 0.1s;
  stroke-linecap: round;
}

.progress-ring__dot {
  transition:
    cx 0.1s,
    cy 0.1s;
}

.time-display {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 1;
}

.date {
  font-size: 16px;
  color: var(--color-text);
  font-weight: 500;
}

.time {
  font-size: 28px;
  font-weight: bold;
  color: var(--color-text);
  line-height: 1.2;
  transition: all 0.3s ease;
}

.time.seconds-mode {
  font-size: 42px;
  letter-spacing: -1px;
}

.time.minimized-mode {
  font-size: 45px;
  line-height: 1;
}

.total-time {
  font-size: 12px;
  color: var(--color-text-secondary);
  opacity: 0.6;
}
</style>
