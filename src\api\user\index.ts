import { request } from '@/utils/request'
export interface loginFormI {
  username: string
  password: string
}
export interface VerificationCodeI {
  username: string
  password: string
  tenantId: string
}
// 登录
export const reqLogin = (userInfo: loginFormI) => {
  const res = request.post({
    url: '/login',
    data: userInfo,
  })
  return res
}
// 获取用户信息
export const reqUserInfo = () => {
  const res = request.get({
    url: '/getUserByToken',
  })
  return res
}
// 获取验证码
export const reqGetVerificationCode = (data: VerificationCodeI) => {
  const res = request.post({
    url: '/getVerificationCode',
    data,
  })
  return res
}
// 获取租户公司列表
export const reqExchangeToken = (data: { code: string }) => {
  const res = request.get({
    url: `/exchange?code=${data.code}`,
  })
  return res
}
// 退出登录
export const reqLogout = () => {
  const res = request.get({
    url: '/loginout',
  })
  return res
}
// 修改密码
export const reqChangePassword = (data: {
  oldPassword: string
  newPassword: string
}) => {
  const res = request.post({
    url: '/updatePassword',
    data,
  })
  return res
}

// 获取租户跳转列表
export const reqGetTenantList = () => {
  const res = request.post({
    url: '/querySystem',
  })
  return res
}

// 切换租户
export const reqChangeTenant = (tenantId: string) => {
  const res = request.get({
    url: `/changeTenant/${tenantId}`,
  })
  return res
}
export const checkApplicationLock = (params?: any) => {
  return request.get({
    url: '/auth-center/checkApplicationLock',
    params,
  })
}
