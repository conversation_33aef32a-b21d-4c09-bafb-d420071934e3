version: '3.8'

services:
  # 前端 Nginx 服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    ports:
      - "80:80"
    volumes:
      # 挂载日志目录
      - ./logs/nginx:/var/log/nginx
      # 如果需要自定义配置，可以挂载配置文件
      # - ./nginx_preview_proxy.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # 后端 API 服务（示例）
  backend:
    # 这里需要替换为您的实际后端服务镜像
    image: your-backend-image:latest
    # 或者使用 build 构建
    # build:
    #   context: ./backend
    #   dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # 后端服务环境变量
      - NODE_ENV=production
      - PORT=8080
    volumes:
      # 挂载后端日志目录
      - ./logs/backend:/app/logs
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  nginx-logs:
  backend-logs:
