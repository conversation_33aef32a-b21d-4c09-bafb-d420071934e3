# PDF预览CORS跨域问题解决方案

## 🎯 问题背景

**原始问题**：
- PDF文件URL：`http://************:8090/object/3gZabmbEVL/s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf`
- 前端应用：`http://localhost:8853`
- 错误信息：`Access-Control-Allow-Origin` header缺失导致CORS跨域阻止

**解决思路**：
使用后端提供的文件预览接口，避免直接访问文件URL产生的跨域问题

## 🔧 实现方案

### 1. URL路径提取函数

```typescript
// 从URL中提取s3路径部分
const extractS3Path = (url: string): string => {
  if (!url) return "";
  
  console.log("提取s3路径 - 原始URL:", url);
  
  // 查找s3/的位置
  const s3Index = url.indexOf('s3/');
  if (s3Index === -1) {
    console.log("URL中未找到s3/路径");
    return "";
  }
  
  // 提取s3/及其后面的部分
  const s3Path = url.substring(s3Index);
  console.log("提取的s3路径:", s3Path);
  
  return s3Path;
};
```

**功能说明**：
- 输入：`http://************:8090/object/3gZabmbEVL/s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf`
- 输出：`s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf`

### 2. PDF预览URL处理函数

```typescript
// PDF预览URL处理 - 使用后端预览接口避免CORS问题
const getPdfPreviewUrl = (url: string): string => {
  if (!url) return "";
  
  // 如果是测试PDF，直接返回
  if (url === "/pdfTest.pdf") {
    return url;
  }
  
  // 如果不使用预览API，返回原始URL
  if (!usePreviewApi.value) {
    console.log("使用原始URL进行PDF预览:", url);
    return getFullUrl(url);
  }
  
  // 提取s3路径
  const s3Path = extractS3Path(url);
  if (!s3Path) {
    console.warn("无法从URL提取s3路径，回退到原始URL:", url);
    return getFullUrl(url);
  }
  
  // 构建后端预览接口URL
  const previewApiUrl = `http://************:8090/api/file/preview?path=${encodeURIComponent(s3Path)}`;
  console.log("PDF预览接口URL:", previewApiUrl);
  
  return previewApiUrl;
};
```

**功能特点**：
- 支持测试PDF文件
- 可配置是否使用预览API
- 自动回退到原始URL处理
- URL编码确保特殊字符正确传递

### 3. 文件下载URL处理函数

```typescript
// 文件下载URL处理 - 使用后端下载接口
const getDownloadUrl = (url: string): string => {
  if (!url) return "";
  
  // 提取s3路径
  const s3Path = extractS3Path(url);
  if (!s3Path) {
    console.warn("无法从URL提取s3路径，使用原始URL进行下载:", url);
    return getFullUrl(url);
  }
  
  // 构建后端下载接口URL
  const downloadApiUrl = `http://************:8090/api/file/download?path=${encodeURIComponent(s3Path)}`;
  console.log("文件下载接口URL:", downloadApiUrl);
  
  return downloadApiUrl;
};
```

### 4. 增强的下载功能

```typescript
const downloadFile = (attachment: ZizhiFujian) => {
  console.log("开始下载文件:", attachment.filename);
  
  const link = document.createElement("a");
  
  // 根据文件类型选择合适的URL处理方式
  if (isPdf(attachment.url)) {
    // PDF文件使用下载接口
    link.href = getDownloadUrl(attachment.url);
  } else {
    // 其他文件类型使用原有逻辑
    link.href = getFullUrl(attachment.url);
  }
  
  link.download = attachment.filename;
  link.target = "_blank"; // 在新窗口打开，避免页面跳转
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  console.log("文件下载链接已触发:", link.href);
};
```

### 5. 模板更新

```vue
<!-- PDF预览 -->
<div v-if="useTestPdf || (displayAttachment && isPdf(displayAttachment.url))" class="pdf-preview">
  <vue-office-pdf
    :src="useTestPdf ? '/pdfTest.pdf' : getPdfPreviewUrl(displayAttachment.url)"
    style="width: 100%; height: 100%"
    @rendered="onPdfRendered"
    @error="onPdfError"
  />
</div>
```

### 6. 调试信息显示

```vue
<!-- 调试信息 -->
<div v-if="isPdf(displayAttachment.url)" class="detail-item debug-info">
  <span class="label">原始URL:</span>
  <span class="value debug-url">{{ displayAttachment.url }}</span>
</div>
<div v-if="isPdf(displayAttachment.url)" class="detail-item debug-info">
  <span class="label">预览URL:</span>
  <span class="value debug-url">{{ getPdfPreviewUrl(displayAttachment.url) }}</span>
</div>
<div v-if="isPdf(displayAttachment.url)" class="detail-item debug-info">
  <span class="label">S3路径:</span>
  <span class="value debug-url">{{ extractS3Path(displayAttachment.url) }}</span>
</div>
```

## 🎨 配置选项

### 预览API开关

```typescript
// 配置选项 - 是否使用新的预览接口（避免CORS问题）
const usePreviewApi = ref(true);
```

**用途**：
- `true`：使用新的预览接口（推荐，解决CORS问题）
- `false`：使用原始URL（用于调试或回退）

## 📊 接口格式

### 预览接口
```
GET http://************:8090/api/file/preview?path=s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
```

### 下载接口
```
GET http://************:8090/api/file/download?path=s3/tanlucloud-conf/files/2025-08/f6757090d53e64d02d54155fb0d7dfc8.pdf
```

## 🔍 调试功能

### 控制台日志
- URL提取过程日志
- 接口URL构建日志
- 错误回退日志

### 界面调试信息
- 原始URL显示
- 预览URL显示
- S3路径显示
- 特殊样式标识调试信息

### CSS调试样式
```scss
.debug-info {
  background-color: #f8f9fa;
  border-left: 3px solid #007bff;
  padding-left: 8px;
  margin-top: 4px;
  font-size: 11px;

  .label {
    color: #007bff;
    font-weight: 600;
  }

  .debug-url {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: #6c757d;
    background-color: #e9ecef;
    padding: 2px 4px;
    border-radius: 2px;
  }
}
```

## ✅ 向后兼容性

### 支持的URL格式
1. **新格式**：包含s3路径的完整URL
2. **旧格式**：相对路径或其他格式
3. **测试格式**：`/pdfTest.pdf`

### 回退机制
1. 无法提取s3路径时，使用原始URL处理
2. 可通过配置关闭新接口，回到原有逻辑
3. 非PDF文件继续使用原有处理方式

## 🧪 测试验证

### 测试场景
1. **正常PDF文件**：包含s3路径的URL
2. **异常URL**：不包含s3路径的URL
3. **测试PDF**：本地测试文件
4. **非PDF文件**：图片等其他格式

### 验证方法
1. 查看控制台日志确认URL处理过程
2. 检查界面调试信息显示
3. 验证PDF预览是否正常加载
4. 测试文件下载功能

## 🎯 预期效果

修改完成后：
- ✅ PDF文件通过预览接口正常显示，无CORS错误
- ✅ 文件下载功能正常工作
- ✅ 多附件切换功能保持正常
- ✅ 向后兼容原有URL格式
- ✅ 提供详细的调试信息
- ✅ 支持配置开关控制功能

现在PDF预览功能已经完全重构，通过后端接口避免了CORS跨域问题！
