import { request } from "@/utils/request";

// 获取企业概览
export const fiveLastday = () => {
  const res = request.post({
    url: "/unit/load/five/lastday",
  });
  return res;
};

// 查询所有污染物排放信息
export const getPollution = () => {
  const res = request.post({
    url: "/pollution/query/all",
  });
  return res;
};

// 查询所有co2排放信息
export const getCo2 = () => {
  const res = request.post({
    url: "/co2/query/all",
  });
  return res;
};

// 获取信丰机组负荷率信息
export const getComplete = () => {
  const res = request.post({
    url: "/unit/load/xfRate",
  });
  return res;
};

// 查询今年发电信息
export const getRateNow = () => {
  const res = request.post({
    url: "/electricity/rate/query/now",
  });
  return res;
};
// 查询去年发电信息
export const getRateLast = () => {
  const res = request.post({
    url: "/electricity/rate/query/last",
  });
  return res;
};

// 查询所有机组概览信息
export const getPlantOverview = () => {
  const res = request.post({
    url: "/plantOverview/query/plantOverview",
  });
  return res;
};

// 机组用电信息
export const getElectricity = () => {
  const res = request.post({
    url: "/electricity/static/query/last",
  });
  return res;
};

// 年累计发电量信息
export const getGenerateYear = () => {
  const res = request.post({
    url: "/power/generate/query/year",
  });
  return res;
};

// 机组用电信息
export const getGenerateMonth = () => {
  const res = request.post({
    url: "/power/generate/query/month",
  });
  return res;
};

// 设备月累计发电量信息
export const getGenerateDevice = () => {
  const res = request.post({
    url: "/power/generate/query/month",
  });
  return res;
};

// 获取1号机组负荷
export const getOneLoad = () => {
  const res = request.post({
    url: "/unit/load/xf1",
  });
  return res;
};

// 获取7号机组负荷
export const getTwoLoad = () => {
  const res = request.post({
    url: "/unit/load/xf2",
  });
  return res;
};
