// 测试脚本：验证 URL 处理功能
// 在浏览器控制台中运行此脚本来测试功能

console.log("=== URL 处理功能测试 ===");

// 测试用例数据
const testCases = [
  {
    name: "S3 路径格式 - PDF",
    url: "/object/68g75PRdRj/s3/tanlucloud-conf/files/2025-08/c78dda64bf81d5bb1961960df7d9d2b7.pdf",
    expectedType: "S3路径",
    description: "应该进行路径转换和API拼接"
  },
  {
    name: "S3 路径格式 - 图片",
    url: "/object/68g75PRdRj/s3/tanlucloud-conf/files/2025-08/c78dda64bf81d5bb1961960df7d9d2b7.png",
    expectedType: "S3路径",
    description: "应该进行路径转换和API拼接"
  },
  {
    name: "完整 HTTP URL - PDF",
    url: "http://************:8891/custom-download/B20V2t9E0WlXtZDgslhJC2HIfO7MCJtaapplication.pdf",
    expectedType: "完整HTTP URL",
    description: "应该直接返回原URL"
  },
  {
    name: "完整 HTTPS URL - PDF",
    url: "https://example.com/files/document.pdf",
    expectedType: "完整HTTP URL",
    description: "应该直接返回原URL"
  },
  {
    name: "相对路径",
    url: "/files/document.pdf",
    expectedType: "相对路径",
    description: "应该进行域名拼接"
  }
];

// 模拟的辅助函数（在实际环境中这些函数已存在）
function isFullHttpUrl(url) {
  return url.startsWith("http://") || url.startsWith("https://");
}

function isS3Path(url) {
  return url.includes("/s3/") && !isFullHttpUrl(url);
}

function extractS3Path(fullUrl) {
  if (!fullUrl) return "";
  
  if (isFullHttpUrl(fullUrl)) {
    console.log("检测到完整HTTP URL，无需提取S3路径:", fullUrl);
    return fullUrl;
  }
  
  const s3Index = fullUrl.indexOf("/s3/");
  if (s3Index === -1) {
    console.warn("URL中未找到s3路径:", fullUrl);
    return fullUrl;
  }
  
  const s3Path = fullUrl.substring(s3Index + 3);
  const result = s3Path.startsWith("/") ? s3Path : "/" + s3Path;
  console.log("提取S3路径:", fullUrl, "->", result);
  return result;
}

function convertPathForApi(s3Path) {
  if (!s3Path) return "";
  
  if (isFullHttpUrl(s3Path)) {
    console.log("检测到完整HTTP URL，无需转换:", s3Path);
    return s3Path;
  }
  
  console.log("开始转换S3路径:", s3Path);
  
  let path = s3Path.startsWith("/") ? s3Path.substring(1) : s3Path;
  const datePattern = /\/(\d{4}-\d{2})\//g;
  path = path.replace(datePattern, "|$1|");
  
  console.log("S3路径转换完成:", s3Path, "->", path);
  return path;
}

function getPdfPreviewUrl(url) {
  if (!url) return "";
  
  console.log("=== 开始获取PDF预览URL ===");
  console.log("输入URL:", url);
  console.log("URL类型检测:");
  console.log("- 是否为完整HTTP URL:", isFullHttpUrl(url));
  console.log("- 是否为S3路径:", isS3Path(url));
  
  if (isFullHttpUrl(url)) {
    console.log("✓ 检测到完整HTTP URL，直接使用");
    console.log("最终预览URL:", url);
    return url;
  }
  
  console.log("✓ 检测到S3路径格式，开始转换处理");
  
  const s3Path = extractS3Path(url);
  console.log("提取的S3路径:", s3Path);
  
  if (isFullHttpUrl(s3Path)) {
    console.log("✓ 提取过程中发现完整URL，直接使用");
    console.log("最终预览URL:", s3Path);
    return s3Path;
  }
  
  const apiPath = convertPathForApi(s3Path);
  console.log("转换后的API路径:", apiPath);
  
  if (isFullHttpUrl(apiPath)) {
    console.log("✓ 转换过程中发现完整URL，直接使用");
    console.log("最终预览URL:", apiPath);
    return apiPath;
  }
  
  const baseURL = "http://************:8891";
  const previewUrl = `${baseURL}/file-preview/${apiPath}`;
  
  console.log("✓ S3路径转换完成");
  console.log("最终预览URL:", previewUrl);
  console.log("=== PDF预览URL获取完成 ===");
  return previewUrl;
}

// 运行测试
console.log("\n开始测试...\n");

testCases.forEach((testCase, index) => {
  console.log(`\n--- 测试用例 ${index + 1}: ${testCase.name} ---`);
  console.log(`描述: ${testCase.description}`);
  console.log(`输入URL: ${testCase.url}`);
  
  try {
    const result = getPdfPreviewUrl(testCase.url);
    console.log(`✅ 测试通过`);
    console.log(`结果URL: ${result}`);
    
    // 验证结果
    if (testCase.expectedType === "完整HTTP URL" && result === testCase.url) {
      console.log("✅ 验证通过：完整HTTP URL直接返回");
    } else if (testCase.expectedType === "S3路径" && result !== testCase.url) {
      console.log("✅ 验证通过：S3路径进行了转换");
    } else {
      console.log("⚠️ 需要人工验证结果是否符合预期");
    }
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
  }
  
  console.log("--- 测试用例结束 ---\n");
});

console.log("=== 所有测试完成 ===");
console.log("\n使用说明：");
console.log("1. 在人员资质预览页面打开浏览器控制台");
console.log("2. 复制并粘贴此脚本");
console.log("3. 按回车执行");
console.log("4. 查看测试结果和日志输出");
