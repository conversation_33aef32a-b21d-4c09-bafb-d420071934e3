# 大数据量传输指南

## 问题描述

当通过URL参数传递大量JSON数据时，可能遇到以下问题：

1. **URL长度限制**：大多数浏览器对URL长度有限制（2048-8192字符）
2. **特殊字符截断**：URL中的`#`字符会导致后续内容被截断
3. **编码问题**：特殊字符未正确编码导致解析失败

## 解决方案

### 1. 使用DataTransferManager（推荐）

```typescript
import { DataTransferManager } from '@/utils/dataTransfer';

// 发送方
const dataTransfer = new DataTransferManager({
  storageKey: 'personnelData',
  urlParamName: 'data'
});

const largeData = [/* 大量数据 */];
const baseUrl = '/personnel-qualification-preview';

const transferInfo = dataTransfer.prepareTransfer(largeData, baseUrl);

if (transferInfo.warning) {
  console.warn(transferInfo.warning);
}

// 跳转到目标页面
window.location.href = transferInfo.url;
```

```typescript
// 接收方（已在personnelQualificationPreview中实现）
const receivedData = dataTransfer.receiveData(route.query);
```

### 2. 手动使用SessionStorage

```typescript
// 发送方
const storageKey = 'personnelData_' + Date.now();
sessionStorage.setItem(storageKey, JSON.stringify(largeData));

const url = `/personnel-qualification-preview?useStorage=true&storageKey=${storageKey}`;
window.location.href = url;
```

### 3. 安全的URL编码

```typescript
import { safeUrlEncode, safeUrlDecode } from '@/utils/dataTransfer';

// 发送方
const encodedData = safeUrlEncode(data);
const url = `/personnel-qualification-preview?data=${encodedData}`;

// 接收方
const decodedData = safeUrlDecode(encodedParam);
```

## 最佳实践

### 1. 数据大小检查

```typescript
const jsonString = JSON.stringify(data);
const encodedString = encodeURIComponent(jsonString);

if (encodedString.length > 2048) {
  console.warn('数据量过大，建议使用sessionStorage传输');
  // 使用sessionStorage方案
} else {
  // 使用URL参数方案
}
```

### 2. 错误处理

```typescript
try {
  const data = JSON.parse(decodeURIComponent(param));
  // 处理数据
} catch (error) {
  console.error('数据解析失败:', error);
  // 尝试从sessionStorage加载备份数据
  const backup = sessionStorage.getItem('backupKey');
  if (backup) {
    const data = JSON.parse(backup);
    // 使用备份数据
  }
}
```

### 3. 数据截断检测

```typescript
import { detectTruncation } from '@/utils/dataTransfer';

const truncationCheck = detectTruncation(jsonString);
if (truncationCheck.isTruncated) {
  console.warn('数据被截断:', truncationCheck.reason);
  // 处理截断情况
}
```

## 常见问题

### Q: 为什么数据在URL中被截断？

A: 主要原因是URL中的`#`字符。浏览器将`#`后的内容视为页面锚点，不会包含在查询参数中。

**解决方法：**
1. 确保数据正确编码：使用`encodeURIComponent()`
2. 使用sessionStorage传输大数据
3. 避免在JSON数据中包含未编码的特殊字符

### Q: 如何判断数据是否完整？

A: 检查JSON字符串的结构完整性：

```typescript
const isComplete = jsonString.trim().endsWith(']') || jsonString.trim().endsWith('}');
if (!isComplete) {
  console.warn('数据可能不完整');
}
```

### Q: URL长度限制是多少？

A: 不同浏览器的限制不同：
- Internet Explorer: 2048字符
- Chrome: 8192字符
- Firefox: 65536字符
- Safari: 80000字符

建议使用保守的2048字符限制。

## 迁移指南

如果你当前使用简单的URL参数传递数据，可以按以下步骤迁移：

1. **安装数据传输工具**：已包含在项目中
2. **更新发送方代码**：使用`DataTransferManager.prepareTransfer()`
3. **更新接收方代码**：使用`DataTransferManager.receiveData()`（personnelQualificationPreview已更新）
4. **测试**：确保大数据量和小数据量都能正常工作

## 示例代码

完整的使用示例：

```typescript
// 发送页面
import { defaultDataTransfer } from '@/utils/dataTransfer';

const sendData = (personnelData: Personnel[]) => {
  const baseUrl = '/personnel-qualification-preview';
  
  try {
    const transferInfo = defaultDataTransfer.prepareTransfer(personnelData, baseUrl);
    
    if (transferInfo.warning) {
      console.warn('数据传输警告:', transferInfo.warning);
    }
    
    console.log('传输方式:', transferInfo.method);
    window.location.href = transferInfo.url;
    
  } catch (error) {
    console.error('数据传输失败:', error);
    // 处理错误
  }
};
```

```typescript
// 接收页面（personnelQualificationPreview/index.vue）
// 已自动处理，无需额外代码
```
