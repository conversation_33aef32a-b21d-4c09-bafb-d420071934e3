#!/bin/bash

# 测试脚本：验证 /preview API 代理配置
# 使用方法：./test-preview-api.sh [域名或IP]

# 默认测试地址
HOST=${1:-"localhost"}
BASE_URL="http://${HOST}"

echo "=== 测试 /preview API 代理配置 ==="
echo "测试地址: ${BASE_URL}"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_request() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: ${description}${NC}"
    echo "请求: ${method} ${url}"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}\nTIME:%{time_total}" \
            -X ${method} \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -d "${data}" \
            "${url}" 2>/dev/null)
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}\nTIME:%{time_total}" \
            -X ${method} \
            -H "Accept: application/json" \
            "${url}" 2>/dev/null)
    fi
    
    # 提取HTTP状态码和响应时间
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    time_total=$(echo "$response" | grep "TIME:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d' | sed '/TIME:/d')
    
    # 判断结果
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 400 ]; then
        echo -e "${GREEN}✓ 成功${NC} (HTTP $http_code, ${time_total}s)"
    elif [ "$http_code" -eq 404 ]; then
        echo -e "${YELLOW}⚠ 接口不存在${NC} (HTTP $http_code)"
    elif [ "$http_code" -eq 502 ] || [ "$http_code" -eq 503 ]; then
        echo -e "${RED}✗ 后端服务不可用${NC} (HTTP $http_code)"
    else
        echo -e "${RED}✗ 失败${NC} (HTTP $http_code)"
    fi
    
    # 显示响应体（如果不为空且不是HTML错误页面）
    if [ -n "$body" ] && [[ ! "$body" =~ "<html>" ]]; then
        echo "响应: $(echo "$body" | head -c 200)..."
    fi
    
    echo ""
}

# 1. 测试 CORS 预检请求
echo "1. 测试 CORS 预检请求"
test_request "OPTIONS" "${BASE_URL}/preview/distribute/queryzizhi" "" "CORS 预检请求"

# 2. 测试实际的 API 请求
echo "2. 测试 API 请求"
test_data='{"biaodanshiliid":"test-id-123"}'
test_request "POST" "${BASE_URL}/preview/distribute/queryzizhi" "$test_data" "查询资质接口"

# 3. 测试静态资源
echo "3. 测试静态资源"
test_request "GET" "${BASE_URL}/" "" "首页访问"
test_request "GET" "${BASE_URL}/index.html" "" "HTML 文件"

# 4. 测试不存在的路径
echo "4. 测试路由处理"
test_request "GET" "${BASE_URL}/personnel-qualification-preview" "" "前端路由"

# 5. 检查 CORS 头
echo "5. 检查 CORS 头"
echo -e "${YELLOW}检查 CORS 响应头:${NC}"
cors_headers=$(curl -s -I -X OPTIONS "${BASE_URL}/preview/distribute/queryzizhi" | grep -i "access-control")
if [ -n "$cors_headers" ]; then
    echo -e "${GREEN}✓ CORS 头存在:${NC}"
    echo "$cors_headers"
else
    echo -e "${RED}✗ 未找到 CORS 头${NC}"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果看到错误，请检查："
echo "1. Nginx 是否正确启动"
echo "2. 后端服务是否运行"
echo "3. 配置文件中的服务器地址是否正确"
echo "4. 防火墙设置是否允许相应端口"
