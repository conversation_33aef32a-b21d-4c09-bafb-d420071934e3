<template>
  <div class="main">
    <div class="left">
      <div class="left-top">
        <el-table :data="data1" border>
          <el-table-column label="累计学习时长">{{
            totalStudyTime
          }}</el-table-column>
          <el-table-column label="课程规定学时">{{
            courseTime
          }}</el-table-column>
          <el-table-column label="培训总时长">{{
            totalCourseTime
          }}</el-table-column>
        </el-table>
      </div>
      <div class="left-center">
        <countdown-timer
          :second="courseTime"
          type="countdown"
          :current-time="
            (studyTime > courseTime ? courseTime : studyTime) * 1000
          "
        ></countdown-timer>
      </div>
      <div class="left-bottom">
        <el-table :data="data2" border>
          <el-table-column label="课程/课件">
            <template #default="scope">
              <el-link type="primary" @click="handleCourseClick(scope.row)">{{
                scope.row.courseName
              }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="是否必学">是</el-table-column>
          <el-table-column label="完成进度">
            <template #default="{ row }">
              <el-progress
                :percentage="
                  Number(
                    (
                      ((row.studyTime > row.courseTime
                        ? row.courseTime
                        : row.studyTime) /
                        row.courseTime) *
                      100
                    ).toFixed(2)
                  )
                "
              ></el-progress>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="right">
      <div class="course-title">{{ courseName }}</div>
      <vue-office-docx
        style="width: 100%"
        v-if="courseType === 'docx'"
        :src="courseSource"
        @mouseenter="handleCourseFocus"
        @mouseleave="handleCourseBlur"
      />
      <vue-office-excel
        style="width: 100%"
        v-else-if="courseType === 'excel'"
        :src="courseSource"
        @mouseenter="handleCourseFocus"
        @mouseleave="handleCourseBlur"
      >
      </vue-office-excel>
      <video
        v-else-if="courseType === 'video'"
        ref="videoRef"
        style="width: 100%"
        controls
        :src="courseSource"
        @contextmenu.prevent
        @seeking="handleSeeking"
        @ratechange="handleRateChange"
        @play="handlePlay"
        @pause="handlePause"
      ></video>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  nextTick,
  ref,
  watch,
  onBeforeUnmount,
  onUnmounted,
  onMounted,
} from "vue";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/docx/lib/index.css";
import "@vue-office/excel/lib/index.css";
import { ElMessage } from "element-plus";
import countdownTimer from "@/components/countdownTimer/index.vue";
const data1 = ref([{}]);
const data2 = ref([
  {
    id: 1,
    courseName: "课程1",
    courseTime: 100,
    studyTime: 0,
    type: "docx",
    src: "/public/word_test.docx",
  },
  {
    id: 2,
    courseName: "课程2",
    courseTime: 10,
    studyTime: 5,
    type: "video",
    src: "/public/video_test.mp4",
  },
  {
    id: 3,
    courseName: "课程3",
    courseTime: 100,
    studyTime: 10,
    type: "excel",
    src: "/public/excel_test.xlsx",
  },
]);
const totalStudyTime = ref(0);
const courseName = ref("");
const courseTime = ref(0);
const totalCourseTime = ref(0);
const courseType = ref("");
const courseId = ref("");
const studyTime = ref(0);
let studyTimeInterval: any = null;
const videoRef = ref();
const courseSource = ref("");
const watchMaxTime = ref(0);
let isUnloading = false; // 标记页面是否正在卸载

const settleCourseStudyTime = () => {
  if (!courseId.value) return;
  if (studyTimeInterval) {
    clearInterval(studyTimeInterval);
    studyTimeInterval = null;
  }
  // 调用后端接口
  localStorage.setItem(
    "studyTime",
    JSON.stringify({ courseId: courseId.value, studyTime: studyTime.value })
  );
  data2.value.find((item: any) => item.id === courseId.value)!.studyTime =
    studyTime.value;
};
const handleSeeking = () => {};

const handleRateChange = () => {
  if (watchMaxTime.value < courseTime.value) {
    // 如果没看完视频，则不允许倍速播放
    videoRef.value.playbackRate = 1;
    // ElMessage.warning('完成学习后才能倍速播放哦~')
  }
};

const handlePlay = () => {};

const handlePause = () => {
  settleCourseStudyTime();
};

const handleTimeupdate = () => {
  //记录视频已经播放时长
  if (videoRef.value.currentTime > watchMaxTime.value) {
    //但如果相差1秒就证明是往后拖时间了 正常来说是每次时长更新相差0.3s
    const num = videoRef.value.currentTime - watchMaxTime.value;
    if (num < 1) {
      //正常播放时，记录当前播放时间
      watchMaxTime.value = videoRef.value.currentTime;
      studyTime.value = Math.floor(watchMaxTime.value);
    } else {
      // 差值大于1s时，视为拖动，将上一次记录的播放时间赋值给当前播放器的播放时间
      videoRef.value.currentTime = watchMaxTime.value;
    }
  }
};

const handleKeydown = (event: any) => {
  if (event.keyCode === 39) {
    //不允许通过方向键快进至超过当前播放的最大视频时间，超过时，将当前播放的最大视频时间赋值给当前播放器播放时间，小于时，不做操作
    if (videoRef.value.currentTime > watchMaxTime.value) {
      videoRef.value.currentTime = watchMaxTime.value;
    }
  }
};

const handleCourseClick = (item: any) => {
  settleCourseStudyTime();
  courseName.value = item.courseName;
  courseTime.value = item.courseTime;
  courseType.value = item.type;
  courseId.value = item.id;
  studyTime.value = item.studyTime;
  courseSource.value = item.src;
  if (courseType.value === "video") {
    watchMaxTime.value = studyTime.value;
    if (studyTime.value >= courseTime.value) {
      return;
    }
    nextTick(() => {
      videoRef.value.addEventListener("timeupdate", handleTimeupdate);
      //监听键盘方向右键
      videoRef.value.addEventListener("keydown", handleKeydown);
    });
  }
};

const handleCourseFocus = () => {
  studyTimeInterval = setInterval(() => {
    studyTime.value += 1;
  }, 1000);
};

watch(
  () => studyTime.value,
  (newVal) => {
    // 实时调用后端更新接口
    console.log(newVal);
  }
);

const handleCourseBlur = () => {
  settleCourseStudyTime();
};

const handleBeforeunload = () => {
  isUnloading = true;
};

const handleVisibilityChange = () => {
  if (document.visibilityState === "hidden" && !isUnloading) {
    if (courseType.value === "video" && videoRef.value) {
      videoRef.value.pause();
    } else {
      settleCourseStudyTime();
    }
  }
};

onMounted(() => {
  window.addEventListener("beforeunload", handleBeforeunload);
  document.addEventListener("visibilitychange", handleVisibilityChange);
});

onBeforeUnmount(() => {
  if (studyTimeInterval) {
    clearInterval(studyTimeInterval);
  }
  if (videoRef.value) {
    videoRef.value.removeEventListener("timeupdate", handleTimeupdate);
    videoRef.value.removeEventListener("keydown", handleKeydown);
  }
  window.removeEventListener("beforeunload", handleUnload);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});
</script>

<style scoped lang="scss">
.main {
  width: 100vw;
  height: 100vh;
  display: flex;
  .left {
    width: 40%;
    .left-top {
    }
    .left-center {
      margin-top: 40px;
      margin-bottom: 40px;
    }
  }
  .right {
    width: 60%;
    .course-title {
    }
  }
}
</style>
