<template>
  <div class="personnel-qualification-preview">
    <div class="preview-content">
      <!-- 左侧人员选择 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>人员选择</h3>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索姓名"
            class="search-input"
            clearable
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="personnel-list">
          <!-- 加载状态 -->
          <div v-if="isLoadingData" class="loading-container">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <span>正在加载人员数据...</span>
          </div>

          <!-- 人员列表 -->
          <div
            v-else
            v-for="person in filteredPersonnelList"
            :key="person.id"
            :class="[
              'personnel-item',
              { active: selectedPersonId === person.id },
            ]"
            @click="selectPerson(person.id)"
          >
            <div class="person-info">
              <span class="person-name">{{
                getPersonDisplayName(person)
              }}</span>
              <el-tag :type="getPersonTagType(person.gongzhong)" size="small">
                {{ getWorkTypeName(person.gongzhong) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 分页组件 - 固定在底部，始终显示 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="totalPersonnel"
            layout="prev, pager, next, jumper"
            :small="true"
            @current-change="handlePageChange"
            :hide-on-single-page="false"
          />
        </div>
      </div>

      <!-- 中间资质类型选择 -->
      <div class="middle-panel">
        <div class="panel-header">
          <h3>类型选择</h3>
        </div>

        <div class="qualification-list">
          <div
            v-for="qualification in currentPersonQualifications"
            :key="qualification.id"
            :class="[
              'qualification-item',
              {
                active: selectedQualificationId == qualification.id,
              },
            ]"
            @click="selectQualification(qualification.id)"
          >
            <div class="qualification-info">
              <span class="qualification-name">
                {{ getQualificationTypeName(qualification.zizhileixing) }}
              </span>
              <div class="upload-status">
                <el-icon
                  v-if="hasUploadedAttachment(qualification.zizhifujian)"
                  color="#00B42A"
                >
                  <CircleCheck />
                </el-icon>
                <el-icon v-else color="#909399">
                  <CircleClose />
                </el-icon>
                <span
                  :class="[
                    'status-text',
                    hasUploadedAttachment(qualification.zizhifujian)
                      ? 'uploaded'
                      : 'not-uploaded',
                  ]"
                >
                  {{
                    hasUploadedAttachment(qualification.zizhifujian)
                      ? `已上传 (${
                          getAttachmentList(qualification.zizhifujian).filter(
                            (att) => att.state === "uploaded"
                          ).length
                        }个文件)`
                      : "未上传"
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧附件预览 -->
      <div class="right-panel">
        <div class="attachment-preview">
          <!-- 多附件切换控制 -->
          <div
            v-if="
              currentAttachments.filter((att) => att.state === 'uploaded')
                .length > 1
            "
            class="attachment-controls"
          >
            <div class="attachment-tabs">
              <el-button
                v-for="(attachment, index) in currentAttachments.filter(
                  (att) => att.state === 'uploaded'
                )"
                :key="attachment.id"
                :type="currentAttachmentIndex === index ? 'primary' : 'default'"
                size="small"
                @click="currentAttachmentIndex = index"
                class="attachment-tab"
              >
                {{ attachment.filename }}
              </el-button>
            </div>
            <div class="attachment-info">
              <span
                >共
                {{
                  currentAttachments.filter((att) => att.state === "uploaded")
                    .length
                }}
                个附件</span
              >
            </div>
          </div>

          <div v-if="displayAttachment || useTestPdf" class="preview-area">
            <!-- 图片预览 -->
            <div
              v-if="
                !useTestPdf &&
                displayAttachment &&
                isImage(displayAttachment.url)
              "
              class="image-preview"
            >
              <img
                :src="getFullUrl(displayAttachment.url)"
                :alt="displayAttachment.filename"
              />
            </div>

            <!-- PDF预览 -->
            <div
              v-if="
                useTestPdf ||
                (displayAttachment && isPdf(displayAttachment.url))
              "
              class="pdf-preview"
            >
              <!-- 加载状态 -->
              <div v-if="isLoadingPdfPreview" class="pdf-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <span>正在加载PDF预览...</span>
              </div>

              <!-- PDF预览iframe -->
              <iframe
                v-else
                :src="currentPdfUrl"
                style="width: 100%; height: 100%; border: none"
                @load="onPdfRendered"
                @error="onPdfError"
              />
            </div>

            <!-- 其他文件类型 -->
            <div
              v-else-if="!isImage(displayAttachment.url)"
              class="file-preview"
            >
              <el-icon size="48"><Document /></el-icon>
              <p>{{ displayAttachment.filename }}</p>
              <p class="file-size">
                文件大小: {{ formatFileSize(displayAttachment.size) }}
              </p>
              <el-button
                type="primary"
                @click="downloadFile(displayAttachment)"
              >
                下载文件
              </el-button>
            </div>

            <!-- 附件详细信息 -->
            <!-- <div class="attachment-details">
              <div class="detail-item">
                <span class="label">文件名:</span>
                <span class="value">{{ displayAttachment.filename }}</span>
              </div>
              <div class="detail-item">
                <span class="label">文件大小:</span>
                <span class="value">{{
                  formatFileSize(displayAttachment.size)
                }}</span>
              </div>
              <div class="detail-item">
                <span class="label">文件类型:</span>
                <span class="value">{{ displayAttachment.mimetype }}</span>
              </div>
            </div> -->
          </div>

          <div v-else class="no-attachment">
            <el-empty description="暂无附件" />
          </div>
        </div>

        <!-- 审核意见 -->
        <div class="review-section">
          <div class="review-header">
            <label>*审核意见</label>
          </div>
          <el-input
            v-model="reviewComment"
            type="textarea"
            :rows="4"
            placeholder="内容"
            class="review-textarea"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  defineExpose,
} from "vue";
import { useRoute } from "vue-router";
import {
  Search,
  CircleCheck,
  CircleClose,
  Document,
  Loading,
} from "@element-plus/icons-vue";
import { defaultDataTransfer, detectTruncation } from "@/utils/dataTransfer";
import { request } from "@/utils/request";
import { queryZizhiAPI } from "@/api/personnelQualificationPreview";

const route = useRoute();

// 接口定义
interface Personnel {
  id: number;
  renyuanxingming: string; // 人员姓名
  gongzhong: string; // 工种
  shoujihaoma: string; // 手机号码
  guishubumen: string; // 归属部门
  yuangongzhuangtai: string; // 员工状态
  qiyemingcheng: string; // 企业名称
  guishubanzu: string; // 归属班组
  zhuanye: string; // 专业
  zizhi_items: ZizhiItem[]; // 资质项目数组
}

interface ZizhiItem {
  id: number;
  renyuanxingming: string;
  zizhileixing: string; // 资质类型
  zizhizhuangtai: number; // 资质状态
  zizhishengxiaoriqi: string; // 生效日期
  zizhishixiaoriqi: string; // 失效日期
  tezhongzuoyecaozuozhengleixing: string; // 特种作业操作证类型
  zizhifujian: ZizhiFujianData; // 资质附件（支持单个或多个）
}

interface ZizhiFujian {
  id: string;
  filename: string;
  name: string;
  size: number;
  mimetype: string;
  state: string;
  value: string;
  url: string;
}

// 支持多个附件的类型定义
type ZizhiFujianData = ZizhiFujian | ZizhiFujian[] | null | undefined;

// 响应式数据
const searchKeyword = ref("");
const selectedPersonId = ref<number | null>(null);
const selectedQualificationId = ref("");
const reviewComment = ref("");

// 测试PDF模式 - 当没有真实附件时使用测试PDF
const useTestPdf = ref(false);

const isLoadingPdfPreview = ref(false);
const isLoadingData = ref(false);

// 人员数据
const personnelList = ref<Personnel[]>([]);
const selectedZizhiItem = ref<ZizhiItem | null>(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalPersonnel = ref(0);

// 父页面通信
const hostUrl = ref("");

// 搜索过滤后的完整列表
const searchFilteredList = computed(() => {
  let filtered = personnelList.value;

  // 搜索过滤 - 支持姓名、工种、ID搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      (person: Personnel) =>
        person.renyuanxingming.includes(searchKeyword.value) ||
        person.gongzhong.includes(searchKeyword.value) ||
        person.id.toString().includes(keyword)
    );
  }

  return filtered;
});

// 分页后的显示列表
const filteredPersonnelList = computed(() => {
  const filtered = searchFilteredList.value;

  // 更新总数
  totalPersonnel.value = filtered.length;

  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;

  return filtered.slice(start, end);
});

// 总页数已在分页组件中自动计算，此处移除未使用的变量

const currentPersonQualifications = computed(() => {
  if (selectedPersonId.value === null) return [];
  const person = personnelList.value.find(
    (p: Personnel) => p.id === selectedPersonId.value
  );
  return person && person.zizhi_items && Array.isArray(person.zizhi_items)
    ? person.zizhi_items
    : [];
});

// 处理多个附件的辅助函数
const getAttachmentList = (zizhifujian: ZizhiFujianData): ZizhiFujian[] => {
  if (!zizhifujian) return [];
  if (Array.isArray(zizhifujian)) return zizhifujian;
  return [zizhifujian];
};

// 检查是否有已上传的附件
const hasUploadedAttachment = (zizhifujian: ZizhiFujianData): boolean => {
  const attachments = getAttachmentList(zizhifujian);
  return attachments.some((att) => att.state === "uploaded");
};

// 移除未使用的getFirstUploadedAttachment函数

// 当前选中的附件列表
const currentAttachments = computed(() => {
  return getAttachmentList(selectedZizhiItem.value?.zizhifujian);
});

// 移除未使用的currentAttachment变量

// 当前选中的附件索引（用于多附件切换）
const currentAttachmentIndex = ref(0);

// 当前显示的附件（支持切换）
const displayAttachment = computed(() => {
  const uploadedAttachments = currentAttachments.value.filter(
    (att: ZizhiFujian) => att.state === "uploaded"
  );

  // 如果没有已上传的附件，启用测试PDF模式
  // if (uploadedAttachments.length === 0) {
  //   useTestPdf.value = true;
  //   return null;
  // }

  // 有附件时关闭测试PDF模式
  useTestPdf.value = false;
  const index = Math.min(
    currentAttachmentIndex.value,
    uploadedAttachments.length - 1
  );
  return uploadedAttachments[index] || null;
});

// 当前PDF预览URL - 响应式计算
const currentPdfUrl = ref<string>("");

// 监听displayAttachment变化，更新PDF预览URL
watch(
  displayAttachment,
  (newAttachment: ZizhiFujian | null) => {
    if (newAttachment && isPdf(newAttachment.url)) {
      currentPdfUrl.value = getPdfPreviewUrl(newAttachment.url);
    } else if (useTestPdf.value) {
      currentPdfUrl.value = "/pdfTest.pdf";
    } else {
      currentPdfUrl.value = "";
    }
  },
  { immediate: true }
);

// 从sessionStorage尝试加载数据的备选方案
const tryLoadFromSessionStorage = () => {
  try {
    const storageKey = "personnelQualificationData";
    const storedData = sessionStorage.getItem(storageKey);

    if (storedData) {
      console.log("从sessionStorage找到数据，长度:", storedData.length);
      const parsedData = JSON.parse(storedData);

      if (Array.isArray(parsedData)) {
        personnelList.value = parsedData;
        console.log(
          "✅ 从sessionStorage加载数据成功，人员数量:",
          parsedData.length
        );
        return true;
      }
    } else {
      console.log("sessionStorage中未找到数据");
    }
  } catch (error) {
    console.error("从sessionStorage加载数据失败:", error);
  }
  return false;
};

// 将数据保存到sessionStorage的辅助函数
const saveToSessionStorage = (data: Personnel[]) => {
  try {
    const storageKey = "personnelQualificationData";
    sessionStorage.setItem(storageKey, JSON.stringify(data));
    console.log("✅ 数据已保存到sessionStorage");
  } catch (error) {
    console.error("保存数据到sessionStorage失败:", error);
  }
};

// API调用函数：获取人员资质数据
const fetchPersonnelQualificationData = async (biaodanshiliid: string) => {
  try {
    isLoadingData.value = true;
    console.log("开始调用接口获取数据，biaodanshiliid:", biaodanshiliid);

    const response = await queryZizhiAPI(biaodanshiliid);

    console.log("接口响应:", response);

    if (response && response.items && Array.isArray(response.items)) {
      // 处理数据格式，确保符合Personnel接口
      const processedData = response.items.map((item: any, index: number) => ({
        id: item.id ? item.id : index + 1, // 生成唯一ID
        renyuanxingming: item.renyuanxingming || item.xingming || "",
        gongzhong: item.gongzhong || "",
        shoujihaoma: item.shoujihaoma || "",
        guishubumen: item.guishubumen || "",
        yuangongzhuangtai: item.yuangongzhuangtai || "",
        qiyemingcheng: item.chengbaoshangmingcheng || "",
        guishubanzu: item.guishubanzu || "",
        zhuanye: item.zhuanye || "",
        zizhi_items:
          item.zizhi_items && Array.isArray(item.zizhi_items)
            ? item.zizhi_items.map((zizhiItem: any, zizhiIndex: number) => ({
                id: zizhiIndex + 1,
                renyuanxingming: item.renyuanxingming || item.xingming || "",
                zizhileixing: zizhiItem.zizhileixing || "",
                zizhizhuangtai: zizhiItem.zizhizhuangtai || 0,
                zizhishengxiaoriqi: zizhiItem.zizhishengxiaoriqi || "",
                zizhishixiaoriqi: zizhiItem.zizhishixiaoriqi || "",
                tezhongzuoyecaozuozhengleixing:
                  zizhiItem.tezhongzuoyecaozuozhengleixing || "",
                zizhifujian: parseZizhiFujian(zizhiItem.zizhifujian),
              }))
            : [],
      }));

      personnelList.value = processedData;
      console.log("✅ 接口数据处理成功，人员数量:", processedData.length);

      // 保存到sessionStorage作为备份
      // saveToSessionStorage(processedData);

      return processedData;
    } else {
      console.warn("接口返回数据格式不正确:", response);
      throw new Error("接口返回数据格式不正确");
    }
  } catch (error) {
    console.error("❌ 获取人员资质数据失败:", error);
    throw error;
  } finally {
    isLoadingData.value = false;
  }
};

// 解析资质附件数据
const parseZizhiFujian = (zizhifujianStr: string): ZizhiFujianData => {
  if (!zizhifujianStr) return null;

  try {
    const parsed = JSON.parse(zizhifujianStr);
    return parsed;
  } catch (error) {
    console.warn("解析资质附件数据失败:", error, zizhifujianStr);
    return null;
  }
};

// 重置分页并选择第一个人员的辅助函数
const resetAndSelectFirst = () => {
  console.log("最终人员列表:", personnelList.value);

  // 重置分页
  currentPage.value = 1;

  // 默认选择第一个人员
  if (Array.isArray(personnelList.value) && personnelList.value.length > 0) {
    const firstPerson = personnelList.value[0];
    if (firstPerson && typeof firstPerson.id !== "undefined") {
      // 延迟执行selectPerson，确保函数已定义
      setTimeout(() => {
        selectPerson(firstPerson.id);
      }, 0);
    }
  }
};

// 数据初始化处理
const initializeData = async (data?: Personnel[]) => {
  console.log("=== 数据初始化开始 ===");

  // 如果直接传入数据，优先使用（保持向后兼容）
  if (data && Array.isArray(data)) {
    personnelList.value = data;
    console.log("直接接收数据:", data);
    // 保存到sessionStorage作为备份
    // saveToSessionStorage(data);

    // 重置分页并选择第一个人员
    resetAndSelectFirst();
    return;
  }

  // 从URL查询参数获取biaodanshiliid
  const biaodanshiliid = route.query.biaodanshiliid as string;
  console.log("从URL获取的biaodanshiliid:", biaodanshiliid);

  if (biaodanshiliid) {
    try {
      // 使用新的API调用获取数据
      await fetchPersonnelQualificationData(biaodanshiliid);

      // 重置分页并选择第一个人员
      resetAndSelectFirst();

      const hostParam = route.query.host as string;
      // 设置父页面URL
      if (hostParam) {
        hostUrl.value = hostParam;
        // 安全地检查是否在iframe中，避免跨域访问问题
        try {
          if (window.parent && window.parent !== window) {
            // 不直接赋值parentWindow，避免跨域访问问题
            console.log("检测到iframe环境");
          }
        } catch (error) {
          console.warn("检查iframe环境时出错:", error);
        }
      }
      return;
    } catch (error) {
      console.error("通过API获取数据失败，尝试备选方案:", error);
      // 如果API调用失败，尝试从sessionStorage加载
      if (tryLoadFromSessionStorage()) {
        resetAndSelectFirst();
        return;
      }
    }
  }

  // 备选方案：尝试从原有的路由参数获取数据（保持向后兼容）
  const pathDataParam = route.params.data as string;
  const queryDataParam = route.query.data as string;
  const hostParam = route.query.host as string;

  console.log("备选方案 - 路由参数 route.params:", route.params);
  console.log("备选方案 - 路由参数 route.query:", route.query);
  console.log("备选方案 - 路径data参数:", pathDataParam);
  console.log("备选方案 - 查询data参数:", queryDataParam);
  console.log("备选方案 - host参数:", hostParam);

  // 设置父页面URL
  if (hostParam) {
    hostUrl.value = hostParam;
    // 安全地检查是否在iframe中，避免跨域访问问题
    try {
      if (window.parent && window.parent !== window) {
        // 不直接赋值parentWindow，避免跨域访问问题
        console.log("检测到iframe环境");
      }
    } catch (error) {
      console.warn("检查iframe环境时出错:", error);
    }
  }

  // 优先使用路径参数，如果没有则使用查询参数
  const dataParam = pathDataParam || queryDataParam;

  // 尝试使用新的数据传输工具
  try {
    console.log("=== 使用数据传输工具解析 ===");

    // 检查是否使用了storage传输
    if (route.query.useStorage === "true" && route.query.storageKey) {
      console.log("检测到storage传输模式");
      // 转换路由查询参数为Record<string, string>格式
      const queryParams: Record<string, string> = {};
      Object.keys(route.query).forEach((key) => {
        const value = route.query[key];
        if (typeof value === "string") {
          queryParams[key] = value;
        } else if (
          Array.isArray(value) &&
          value.length > 0 &&
          typeof value[0] === "string"
        ) {
          queryParams[key] = value[0];
        }
      });

      const parsedData = defaultDataTransfer.receiveData(queryParams);
      if (Array.isArray(parsedData)) {
        personnelList.value = parsedData;
        console.log("✅ 从storage加载数据成功，人员数量:", parsedData.length);
        resetAndSelectFirst();
        return;
      }
    }

    // 尝试从URL参数解析
    if (dataParam) {
      console.log("原始数据参数长度:", dataParam.length);
      console.log("原始数据参数前100字符:", dataParam.substring(0, 100));
      console.log(
        "原始数据参数后100字符:",
        dataParam.substring(dataParam.length - 100)
      );

      // 检查数据是否被截断
      const truncationCheck = detectTruncation(dataParam);
      if (truncationCheck.isTruncated) {
        console.warn("⚠️ 数据被截断:", truncationCheck.reason);
        console.warn(
          "建议使用DataTransferManager.prepareTransfer()方法传递大量数据"
        );

        // 尝试从sessionStorage获取数据作为备选方案
        if (tryLoadFromSessionStorage()) {
          resetAndSelectFirst();
          return;
        }
      }

      // 尝试解析数据
      const parsedData = defaultDataTransfer.receiveData({ data: dataParam });

      if (Array.isArray(parsedData)) {
        personnelList.value = parsedData;
        console.log("✅ 数据解析成功，人员数量:", parsedData.length);
        // 保存到sessionStorage作为备份
        saveToSessionStorage(parsedData);
        resetAndSelectFirst();
        return;
      } else {
        console.warn("⚠️ 解析的数据不是数组格式");
      }
    } else {
      console.log("未找到data参数，尝试从sessionStorage加载");
      if (tryLoadFromSessionStorage()) {
        resetAndSelectFirst();
        return;
      }
    }
  } catch (error) {
    console.error("❌ 数据传输工具解析失败:", error);
    console.error("错误详情:", {
      message: error instanceof Error ? error.message : String(error),
      dataLength: dataParam?.length || 0,
      dataStart: dataParam?.substring(0, 200) || "",
      dataEnd:
        dataParam?.substring(Math.max(0, (dataParam?.length || 0) - 200)) || "",
    });

    // 最后的备选方案：从sessionStorage加载
    console.log("尝试最后的备选方案：从sessionStorage加载");
    if (tryLoadFromSessionStorage()) {
      resetAndSelectFirst();
      return;
    }
  }

  // 最终调用重置和选择函数（如果前面的所有方法都失败了）
  resetAndSelectFirst();
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  console.log("切换到第", page, "页");
};

// 监听搜索关键词变化，重置页码
watch(searchKeyword, () => {
  currentPage.value = 1;
  console.log("搜索关键词变化，重置到第1页");
});

// 监听选中的资质变化，重置附件索引
watch(selectedZizhiItem, () => {
  currentAttachmentIndex.value = 0;
  console.log("选中资质变化，重置附件索引");
});

// 生成人员显示名称（包含ID以区分重名）
const getPersonDisplayName = (person: Personnel) => {
  // return `${person.renyuanxingming} (ID: ${person.id})`;
  return `${person.renyuanxingming}`;
};

const selectPerson = (personId: number) => {
  selectedPersonId.value = personId;
  selectedQualificationId.value = "";
  selectedZizhiItem.value = null;

  const person = personnelList.value.find((p: Personnel) => p.id === personId);
  console.log(
    "选择人员:",
    person ? getPersonDisplayName(person) : `ID: ${personId}`
  );

  // 默认选择第一个资质项目
  if (
    person &&
    person.zizhi_items &&
    Array.isArray(person.zizhi_items) &&
    person.zizhi_items.length > 0
  ) {
    selectQualification(person.zizhi_items[0].id);
  }
};

const selectQualification = (qualificationId: number) => {
  selectedQualificationId.value = qualificationId.toString();
  console.log("选择资质类型:", qualificationId);

  // 找到对应的资质项目
  const person = personnelList.value.find(
    (p: Personnel) => p.id === selectedPersonId.value
  );
  if (person && person.zizhi_items && Array.isArray(person.zizhi_items)) {
    selectedZizhiItem.value =
      person.zizhi_items.find(
        (item: ZizhiItem) => item.id === qualificationId
      ) || null;
  }
};

const getPersonTagType = (gongzhong: string) => {
  // 根据工种返回标签类型
  switch (gongzhong) {
    case "8": // 安装人员
      return "warning";
    case "jiazigong": // 架子工
    case "7": // 普通工人
      return "info";
    default:
      return "info";
  }
};

// 工种枚举映射
const workTypeOptions = [
  { label: "普工", value: "1" },
  { label: "电工", value: "2" },
  { label: "架子工", value: "3" },
  { label: "焊工", value: "4" },
  { label: "登高作业工", value: "5" },
  { label: "输煤检修工", value: "6" },
  { label: "锅炉检修工", value: "7" },
  { label: "汽机检修工", value: "8" },
  { label: "运行值班员", value: "9" },
  { label: "运行巡检员", value: "10" },
  { label: "电动机检修工", value: "11" },
  { label: "起重司机", value: "12" },
  { label: "起重指挥工", value: "13" },
  { label: "起重司索工", value: "14" },
  { label: "驾驶员", value: "15" },
  { label: "锅炉司炉工", value: "16" },
  { label: "化验员", value: "17" },
  { label: "汽车驾驶员", value: "18" },
  { label: "锅炉运行值班员", value: "19" },
  { label: "燃料值班员", value: "20" },
  { label: "汽轮机运行值班员", value: "21" },
  { label: "燃气轮机值班员", value: "22" },
  { label: "发电集值班员控", value: "23" },
  { label: "电气值班员", value: "24" },
  { label: "火电厂氢冷值班员", value: "25" },
  { label: "余热余压利用系统操作工", value: "26" },
  { label: "水力发电运行值班员", value: "27" },
  { label: "光伏发电运维值班员", value: "28" },
  { label: "锅炉操作工", value: "29" },
  { label: "风力发电运维值班员", value: "30" },
  { label: "供热管网系统运行工", value: "31" },
  { label: "变配电运行值班员", value: "32" },
  { label: "继电保护员", value: "33" },
  { label: "燃气储运工", value: "34" },
  { label: "气体深冷分离工", value: "35" },
  { label: "工业气体生产工", value: "36" },
  { label: "工业气体生产工", value: "37" },
  { label: "工业气体液化工", value: "38" },
  { label: "工业废气治理工", value: "39" },
  { label: "压缩机操作工", value: "40" },
  { label: "风机操作工", value: "41" },
  { label: "水生产处理工", value: "42" },
  { label: "水供应输排工", value: "43" },
  { label: "工业废水处理工", value: "44" },
  { label: "司泵工", value: "45" },
  { label: "管廊运维员", value: "46" },
  { label: "砌筑工", value: "47" },
  { label: "石工", value: "48" },
  { label: "混凝土工", value: "49" },
  { label: "钢筋工", value: "50" },
  { label: "装配式建筑施工员", value: "51" },
  { label: "乡村建设工匠", value: "52" },
  { label: "铁路自轮运转设备工", value: "53" },
  { label: "铁路线桥工", value: "54" },
  { label: "筑路工", value: "55" },
  { label: "公路养护工", value: "56" },
  { label: "桥隧工", value: "57" },
  { label: "凿岩工", value: "58" },
  { label: "爆破工", value: "59" },
  { label: "防水工", value: "60" },
  { label: "水运工程施工工", value: "61" },
  { label: "水工建构筑物维护检修工", value: "62" },
  { label: "电力电缆安装运维工", value: "63" },
  { label: "送配电线路工", value: "64" },
  { label: "牵引电力线路安装维护工", value: "65" },
  { label: "舟桥工", value: "66" },
  { label: "管道工", value: "67" },
  { label: "铁路综合维修工", value: "68" },
  { label: "城市轨道交通检修工", value: "69" },
  { label: "机械设备安装工", value: "70" },
  { label: "电气设备安装工", value: "71" },
  { label: "电梯安装维修工", value: "72" },
  { label: "管工", value: "73" },
  { label: "制冷空调系统安装维修工", value: "74" },
  { label: "锅炉设备安装工", value: "75" },
  { label: "发电设备安装工", value: "76" },
  { label: "电力电气设备安装工", value: "77" },
  { label: "装饰装修工", value: "78" },
  { label: "建筑门窗幕墙安装工", value: "79" },
  { label: "照明工程施工员", value: "80" },
  { label: "专用车辆驾驶员", value: "81" },
  { label: "起重装卸机械操作工", value: "82" },
  { label: "起重工", value: "83" },
  { label: "输送机操作工", value: "84" },
  { label: "索道运输机械操作工", value: "85" },
  { label: "挖掘铲运和桩工机械司机", value: "86" },
  { label: "设备点检员", value: "87" },
  { label: "机修钳工", value: "88" },
  { label: "仪器仪表维修工", value: "89" },
  { label: "锅炉设备检修工", value: "90" },
  { label: "汽轮机和水轮机检修工", value: "91" },
  { label: "电机检修工", value: "92" },
  { label: "变电设备检修工", value: "93" },
  { label: "工程机械维修工", value: "94" },
  { label: "机电设备维修工", value: "95" },
  { label: "船舶修理工", value: "96" },
  { label: "化学检验员", value: "97" },
  { label: "物理性能检验员", value: "98" },
  { label: "生化检验员", value: "99" },
  { label: "无损检测员", value: "100" },
  { label: "质检员", value: "101" },
  { label: "试验员", value: "102" },
  { label: "称重计量工", value: "103" },
  { label: "包装工", value: "104" },
  { label: "安全员", value: "105" },
  { label: "工业机器人系统运维员", value: "106" },
  { label: "工业视觉系统运维员", value: "107" },
  { label: "其他", value: "108" },
  { label: "单位负责人", value: "109" },
  { label: "物业外委", value: "110" },
  { label: "机务", value: "111" },
];

const getWorkTypeName = (gongzhong: string) => {
  // 兼容旧的字符串格式
  if (gongzhong === "jiazigong") return "架子工";

  // 根据工种代码返回工种名称
  const workType = workTypeOptions.find((item) => item.value === gongzhong);
  return workType ? workType.label : `工种${gongzhong}`;
};

// 人员资质类型枚举映射
const qualificationTypeOptions = [
  { label: "身份证_国徽面", value: "1" },
  { label: "身份证_人像面", value: "2" },
  { label: "劳动合同", value: "3" },
  { label: "保险", value: "4" },
  { label: "体检报告", value: "5" },
  { label: "特种作业操作证", value: "6" },
];

const getQualificationTypeName = (zizhileixing: string) => {
  // 根据资质类型代码返回资质类型名称
  const qualificationType = qualificationTypeOptions.find(
    (item) => item.value === zizhileixing
  );
  return qualificationType
    ? qualificationType.label
    : `资质类型${zizhileixing}`;
};

const formatFileSize = (bytes: number) => {
  // 格式化文件大小
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// URL处理 - 支持相对路径和完整URL
const getFullUrl = (url: string) => {
  if (!url) return "";

  console.log("处理URL:", url);

  // 如果已经是完整URL，直接返回
  if (url.startsWith("http://") || url.startsWith("https://")) {
    console.log("检测到完整HTTP URL，直接返回:", url);
    return url;
  }

  // 对于相对路径，进行拼接处理
  console.log("检测到相对路径，进行拼接处理");

  // 如果有host参数，使用host作为基础URL
  if (hostUrl.value) {
    console.log("使用Host URL:", hostUrl.value);
    console.log("原始URL:", url);

    // 确保url以/开头，然后拼接到固定的基础URL
    const relativePath = url.startsWith("/") ? url : "/" + url;
    const fullUrl = "http://************:8090" + relativePath;

    console.log("生成的完整URL:", fullUrl);
    return fullUrl;
  }

  // 否则使用当前域名
  const fullUrl = window.location.origin + "/" + url.replace(/^\//, "");
  console.log("使用当前域名生成URL:", fullUrl);
  return fullUrl;
};

/**
 * 检查URL是否为完整的HTTP/HTTPS链接
 * @param url 要检查的URL
 * @returns 是否为完整的HTTP/HTTPS链接
 */
const isFullHttpUrl = (url: string): boolean => {
  return url.startsWith("http://") || url.startsWith("https://");
};

/**
 * 检查URL是否为S3路径格式
 * @param url 要检查的URL
 * @returns 是否为S3路径格式
 */
const isS3Path = (url: string): boolean => {
  return url.includes("/s3/") && !isFullHttpUrl(url);
};

const isImage = (url: string) => {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
};

const isPdf = (url: string) => {
  return /\.pdf$/i.test(url);
};

/**
 * 转换S3路径为API路径格式
 * @param s3Path S3路径或完整URL
 * @returns 转换后的API路径或原URL
 */
const convertPathForApi = (s3Path: string): string => {
  if (!s3Path) return "";

  try {
    // 如果是完整的HTTP/HTTPS URL，直接返回（不需要转换）
    if (s3Path.startsWith("http://") || s3Path.startsWith("https://")) {
      console.log("检测到完整HTTP URL，无需转换:", s3Path);
      return s3Path;
    }

    // 对S3路径进行转换处理
    console.log("开始转换S3路径:", s3Path);

    // 移除开头的斜杠（如果有）
    let path = s3Path.startsWith("/") ? s3Path.substring(1) : s3Path;

    // 使用正则表达式匹配日期格式的目录（如 2025-08, 2024-12 等）
    // 匹配模式：4位数字-2位数字
    const datePattern = /\/(\d{4}-\d{2})\//g;

    // 将日期目录前后的斜杠替换为竖线
    path = path.replace(datePattern, "|$1|");

    console.log("S3路径转换完成:", s3Path, "->", path);
    return path;
  } catch (error) {
    console.error("路径转换失败:", error, s3Path);
    return s3Path;
  }
};

/**
 * 从完整URL中提取S3路径
 * @param fullUrl 完整的文件URL
 * @returns 提取后的S3路径，不包含s3前缀
 */
const extractS3Path = (fullUrl: string): string => {
  if (!fullUrl) return "";

  try {
    // 如果是完整的HTTP/HTTPS URL，直接返回（不需要提取S3路径）
    if (fullUrl.startsWith("http://") || fullUrl.startsWith("https://")) {
      console.log("检测到完整HTTP URL，无需提取S3路径:", fullUrl);
      return fullUrl;
    }

    // 查找s3/的位置
    const s3Index = fullUrl.indexOf("/s3/");
    if (s3Index === -1) {
      console.warn("URL中未找到s3路径:", fullUrl);
      return fullUrl; // 如果没有找到s3，返回原URL作为fallback
    }

    // 提取s3/之后的路径，并在前面加上/
    const s3Path = fullUrl.substring(s3Index + 3); // +3 是为了跳过 '/s3'
    const result = s3Path.startsWith("/") ? s3Path : "/" + s3Path;
    console.log("提取S3路径:", fullUrl, "->", result);
    return result;
  } catch (error) {
    console.error("提取S3路径失败:", error, fullUrl);
    return fullUrl;
  }
};

// 获取PDF预览URL - 支持两种URL格式
const getPdfPreviewUrl = (url: string): string => {
  if (!url) return "";

  try {
    // 如果是测试PDF，直接返回
    if (useTestPdf.value) {
      return "/pdfTest.pdf";
    }

    console.log("=== 开始获取PDF预览URL ===");
    console.log("输入URL:", url);
    console.log("URL类型检测:");
    console.log("- 是否为完整HTTP URL:", isFullHttpUrl(url));
    console.log("- 是否为S3路径:", isS3Path(url));

    // 情况1：完整的HTTP/HTTPS URL，直接返回
    if (isFullHttpUrl(url)) {
      console.log("✓ 检测到完整HTTP URL，直接使用");
      console.log("最终预览URL:", url);
      return url;
    }

    // 情况2：S3路径格式，进行转换处理
    console.log("✓ 检测到S3路径格式，开始转换处理");

    // 从URL中提取S3路径
    const s3Path = extractS3Path(url);
    console.log("提取的S3路径:", s3Path);

    // 如果提取后仍然是完整URL（extractS3Path的fallback逻辑），直接返回
    if (isFullHttpUrl(s3Path)) {
      console.log("✓ 提取过程中发现完整URL，直接使用");
      console.log("最终预览URL:", s3Path);
      return s3Path;
    }

    // 转换路径格式
    const apiPath = convertPathForApi(s3Path);
    console.log("转换后的API路径:", apiPath);

    // 如果转换后仍然是完整URL（convertPathForApi的fallback逻辑），直接返回
    if (isFullHttpUrl(apiPath)) {
      console.log("✓ 转换过程中发现完整URL，直接使用");
      console.log("最终预览URL:", apiPath);
      return apiPath;
    }

    // 构建完整的预览URL
    const baseURL = "http://************:8891";
    const previewUrl = `${baseURL}/file-preview/${apiPath}`;

    console.log("✓ S3路径转换完成");
    console.log("最终预览URL:", previewUrl);
    console.log("=== PDF预览URL获取完成 ===");
    return previewUrl;
  } catch (error) {
    console.error("❌ 获取PDF预览URL失败:", error);
    console.log("使用fallback方案:", getFullUrl(url));
    // 最终fallback到原URL
    return getFullUrl(url);
  }
};

const downloadFile = (attachment: ZizhiFujian) => {
  console.log("=== 开始下载文件 ===");
  console.log("文件信息:", {
    filename: attachment.filename,
    url: attachment.url,
    size: attachment.size,
  });

  try {
    // 获取正确的下载URL
    let downloadUrl: string;

    if (isFullHttpUrl(attachment.url)) {
      // 完整HTTP URL，直接使用
      downloadUrl = attachment.url;
      console.log("✓ 使用完整HTTP URL进行下载:", downloadUrl);
    } else {
      // 相对路径，使用getFullUrl处理
      downloadUrl = getFullUrl(attachment.url);
      console.log("✓ 使用相对路径拼接URL进行下载:", downloadUrl);
    }

    // 创建下载链接
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.download = attachment.filename;
    link.style.display = "none";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log("✓ 文件下载触发成功");
    console.log("=== 文件下载完成 ===");
  } catch (error) {
    console.error("❌ 文件下载失败:", error);
  }
};

// PDF预览相关方法
const onPdfRendered = () => {
  console.log("PDF渲染成功");
};

const onPdfError = (error: any) => {
  console.error("PDF渲染失败:", error);
};

// 速搭广播监听处理
const handleSudaBroadcast = (event: MessageEvent) => {
  console.log("=== 速搭广播监听器 ===");
  console.log("事件源:", event.origin);
  console.log("原始数据:", event.data);

  let messageData = event.data;

  // 如果数据是字符串，尝试解析JSON
  if (typeof event.data === "string") {
    try {
      messageData = JSON.parse(event.data);
      console.log("解析后的消息数据:", messageData);
    } catch (e) {
      console.log("消息不是JSON格式，直接使用:", event.data);
      return;
    }
  }

  // 检查多种可能的广播消息格式 - 更宽松的检查
  const isSpeedaBroadcast =
    (messageData && messageData.type === "broadcast") ||
    (messageData && messageData.actionType === "broadcast") ||
    (messageData && messageData.eventName) ||
    (messageData && messageData.eventName === "ff28d12e814a_dialogConfirm") ||
    (messageData &&
      typeof messageData === "object" &&
      Object.keys(messageData).length > 0);

  console.log("广播检查结果:", isSpeedaBroadcast);
  console.log("消息数据详情:", {
    hasType: messageData && "type" in messageData,
    hasActionType: messageData && "actionType" in messageData,
    hasEventName: messageData && "eventName" in messageData,
    isObject: typeof messageData === "object",
    keys:
      messageData && typeof messageData === "object"
        ? Object.keys(messageData)
        : [],
  });

  if (isSpeedaBroadcast) {
    console.log("=== 接收到速搭广播消息 ===");

    // 获取事件名称（支持多种格式）
    const eventName =
      messageData.eventName ||
      messageData.type ||
      messageData.action ||
      messageData.name ||
      "unknown";
    const eventData =
      messageData.data ||
      messageData.payload ||
      messageData.args ||
      messageData;

    console.log("事件名称:", eventName);
    console.log("广播数据:", eventData);

    // 根据不同的广播事件执行相应操作
    switch (eventName) {
      case "ff28d12e814a_dialogConfirm":
      case "broadcast_1":
        handleBroadcast1(eventData);
        break;
      case "personnel_update":
        handlePersonnelUpdate(eventData);
        break;
      case "refresh_data":
        handleRefreshData(eventData);
        break;
      default:
        console.log("未处理的广播事件:", eventName);
        console.log("尝试通用处理...");
        handleGenericBroadcast(eventName, eventData);
    }
  } else {
    console.log("非广播消息，跳过处理");
  }
};

// 处理broadcast_1事件
const handleBroadcast1 = (data: any) => {
  console.log("处理broadcast_1事件:", data);

  // 返回审核意见
  sendMessageToParent("reviewComment", {
    comment: reviewComment.value,
    selectedPerson: selectedPersonId.value,
    selectedQualification: selectedQualificationId.value,
  });
  // 可以在这里添加具体的业务逻辑
  // 例如：更新界面状态、刷新数据等
};

// 处理人员更新事件
const handlePersonnelUpdate = (data: any) => {
  console.log("处理人员更新事件:", data);

  if (data.personnelList && Array.isArray(data.personnelList)) {
    // 更新人员列表
    initializeData(data.personnelList);
    console.log("通过广播更新人员列表");
  }
};

// 处理数据刷新事件
const handleRefreshData = (data: any) => {
  console.log("处理数据刷新事件:", data);

  // 重新初始化数据
  initializeData();
  console.log("通过广播刷新数据");
};

// 通用广播事件处理
const handleGenericBroadcast = (eventName: string, data: any) => {
  console.log("通用广播处理:", eventName, data);

  // 可以在这里添加通用的处理逻辑
  // 例如：记录事件、更新状态等

  // 如果是对话框确认事件，返回审核意见
  if (eventName.includes("dialogConfirm") || eventName.includes("confirm")) {
    console.log("检测到确认事件，返回审核意见");
    sendMessageToParent("reviewComment", {
      comment: reviewComment.value,
      selectedPerson: selectedPersonId.value,
      selectedQualification: selectedQualificationId.value,
      eventName: eventName,
      timestamp: Date.now(),
    });
  }
};

// 父子页面通信功能
const sendMessageToParent = (type: string, data: any) => {
  try {
    // 检查是否在iframe中
    if (window.parent && window.parent !== window) {
      const message = {
        type,
        data,
        source: "personnel-qualification-preview",
      };
      console.log("发送消息给父页面:", message);
      window.parent.postMessage(message, "*");
    } else {
      console.log("不在iframe环境中，无法发送消息给父页面");
    }
  } catch (error) {
    console.warn("发送消息给父页面失败:", error);
  }
};

const handleParentMessage = (event: MessageEvent) => {
  console.log("接收到消息:", event.data);

  if (event.data && event.data.type) {
    switch (event.data.type) {
      case "updateData":
        // 更新数据
        if (event.data.data && Array.isArray(event.data.data)) {
          personnelList.value = event.data.data;
          currentPage.value = 1;
          console.log("更新人员数据:", event.data.data);

          // 默认选择第一个人员
          if (personnelList.value.length > 0) {
            selectPerson(personnelList.value[0].id);
          }
        }
        break;
      case "setPersonnelData":
        // 直接设置人员数据
        if (event.data.data && Array.isArray(event.data.data)) {
          console.log("直接设置人员数据:", event.data.data);
          initializeData(event.data.data);
        }
        break;
      case "getReviewComment":
        // 返回审核意见
        sendMessageToParent("reviewComment", {
          comment: reviewComment.value,
          selectedPerson: selectedPersonId.value,
          selectedQualification: selectedQualificationId.value,
        });
        break;
      case "clearData":
        // 清空数据
        personnelList.value = [];
        selectedPersonId.value = null;
        selectedQualificationId.value = "";
        selectedZizhiItem.value = null;
        reviewComment.value = "";
        currentPage.value = 1;
        break;
    }
  }
};

// 暴露审核意见给外部调用
const getReviewComment = () => {
  return reviewComment.value;
};

// 直接设置数据的方法
const setPersonnelData = (data: Personnel[]) => {
  console.log("直接设置人员数据:", data);
  initializeData(data);
};

// 暴露方法给外部调用
defineExpose({
  getReviewComment,
  sendMessageToParent,
  setPersonnelData,
  initializeData,
});

onMounted(async () => {
  // 组件挂载后初始化数据
  await initializeData();

  // 监听速搭广播消息
  window.addEventListener("message", handleSudaBroadcast);

  // 添加全局消息监听用于调试
  window.addEventListener("message", (event: MessageEvent) => {
    console.log("=== 全局消息监听 ===");
    console.log("事件源:", event.origin);
    console.log("事件数据:", event.data);
    console.log("事件类型:", typeof event.data);
    console.log("完整事件对象:", event);

    // 检查是否是字符串格式的JSON
    if (typeof event.data === "string") {
      try {
        const parsedData = JSON.parse(event.data);
        console.log("解析后的数据:", parsedData);
      } catch (e) {
        console.log("数据不是JSON格式:", event.data);
      }
    }

    // 特别检查速搭平台的广播格式
    if (event.data && typeof event.data === "object") {
      // 检查是否包含广播相关的字段
      const hasEventName = "eventName" in event.data;
      const hasActionType = "actionType" in event.data;
      const hasType = "type" in event.data;

      console.log("广播字段检查:");
      console.log("- hasEventName:", hasEventName);
      console.log("- hasActionType:", hasActionType);
      console.log("- hasType:", hasType);

      if (
        hasEventName ||
        hasActionType ||
        (hasType && event.data.type === "broadcast")
      ) {
        console.log("🎯 检测到可能的广播消息，手动触发处理");
        handleSudaBroadcast(event);
      }
    }
  });

  // 向父页面发送准备就绪消息
  // setTimeout(() => {
  //   sendMessageToParent("ready", {
  //     message: "子页面已准备就绪",
  //     timestamp: Date.now(),
  //   });

  //   // 测试：主动发送一个测试消息给自己
  //   console.log("发送测试消息...");
  //   window.postMessage(
  //     {
  //       type: "test",
  //       eventName: "self_test",
  //       data: { message: "这是一个自测消息" },
  //     },
  //     "*"
  //   );
  // }, 1000);
});

// 组件卸载时清理事件监听
const cleanup = () => {
  window.removeEventListener("message", handleParentMessage);
  window.removeEventListener("message", handleSudaBroadcast);
};

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});

// 监听页面卸载
window.addEventListener("beforeunload", cleanup);
</script>

<style scoped lang="scss">
.personnel-qualification-preview {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .preview-content {
    display: flex;
    height: 600px;
    border: 1px solid #e5e6eb;

    .left-panel {
      width: 334px;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;
      position: relative;
      height: 100%; // 确保占满高度

      .panel-header {
        padding: 16px 16px 12px 16px;
        border-bottom: 1px solid #e5e6eb;
        flex-shrink: 0;

        h3 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }

        .search-input {
          :deep(.el-input__wrapper) {
            border-radius: 2px;
          }
        }
      }

      .personnel-list {
        flex-direction: column;
        justify-content: flex-start; // 改为flex-start，移除间距
        align-content: flex-start; // 改为flex-start，移除间距
        display: flex;
        flex: 1;
        overflow-y: auto;
        padding: 0;
        gap: 0; // 确保没有gap间距
        min-height: 0; // 允许flex子项收缩

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f2f3f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
          border-radius: 3px;

          &:hover {
            background: #a8abb2;
          }
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 16px;
          color: #909399;
          font-size: 14px;
          gap: 12px;

          .el-icon {
            font-size: 24px;
            animation: rotate 2s linear infinite;
          }

          @keyframes rotate {
            from {
              transform: rotate(0deg);
            }
            to {
              transform: rotate(360deg);
            }
          }
        }

        .personnel-item {
          padding: 12px 16px;
          min-height: 48px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 0;
          margin: 0; // 确保没有外边距
          border-bottom: 1px solid #f2f3f5;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 3px solid #165dff;
            margin-left: 0;
            padding-left: 13px; // 减少左边距以补偿边框

            .person-name {
              color: #165dff;
            }
          }

          // 移除最后一个项目的底部边框
          &:last-child {
            border-bottom: none;
          }

          .person-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .person-name {
              font-size: 14px;
              color: #121314;
            }

            .el-tag {
              border-radius: 34px;
              font-size: 12px;
            }
          }
        }
      }

      .pagination-wrapper {
        padding: 8px 16px;
        border-top: 1px solid #e5e6eb;
        display: flex;
        justify-content: center;
        background-color: #fff;
        flex-shrink: 0; // 防止被压缩，固定在底部
        min-height: 40px; // 固定最小高度
        position: relative; // 确保在正确的层级

        :deep(.el-pagination) {
          .el-pager li {
            min-width: 20px;
            height: 20px;
            line-height: 20px;
            font-size: 11px;
            margin: 0 2px;
            border-radius: 2px;
          }

          .btn-prev,
          .btn-next {
            min-width: 20px;
            height: 20px;
            line-height: 20px;
            font-size: 11px;
            margin: 0 2px;

            .el-icon {
              font-size: 10px;
            }
          }

          // 当只有一页时也显示分页组件
          &.is-background .btn-prev,
          &.is-background .btn-next {
            background-color: #f5f7fa;
          }

          // 禁用状态样式
          .btn-prev:disabled,
          .btn-next:disabled {
            color: #c0c4cc;
            cursor: not-allowed;
          }
        }
      }
    }

    .middle-panel {
      flex: 0.8;
      flex-shrink: 0;
      border-right: 1px solid #e5e6eb;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 16px;
        border-bottom: 1px solid #e5e6eb;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 700;
          color: #121314;
        }
      }

      .qualification-list {
        flex: 1;
        overflow-y: auto;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f2f3f5;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
          border-radius: 3px;

          &:hover {
            background: #a8abb2;
          }
        }

        .qualification-item {
          padding: 0 16px;
          height: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          border-radius: 2px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f0ff;
            border-left: 2px solid #165dff;

            .qualification-name {
              color: #165dff;
            }
          }

          &.required {
            padding-left: 27px;
          }

          .qualification-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .qualification-name {
              font-size: 14px;
              color: #121314;
            }

            .upload-status {
              display: flex;
              align-items: center;
              gap: 4px;

              .status-text {
                font-size: 12px;

                &.uploaded {
                  color: #00b42a;
                }

                &.not-uploaded {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .right-panel {
      flex: 2;
      display: flex;
      flex-direction: column;
      padding: 24px;
      gap: 16px;

      .attachment-preview {
        flex: 1;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .attachment-controls {
          padding: 12px;
          border-bottom: 1px solid #e5e6eb;
          background-color: #fafafa;

          .attachment-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;

            .attachment-tab {
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .attachment-info {
            font-size: 12px;
            color: #909399;
          }
        }

        .preview-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .image-preview {
            flex: 1;
            overflow: auto;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          }

          .pdf-preview {
            flex: 1;
            overflow: hidden;
            position: relative;

            iframe {
              width: 100%;
              height: 100%;
            }

            .pdf-loading {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 12px;
              color: #909399;
              font-size: 14px;

              .el-icon {
                font-size: 24px;
                animation: rotate 2s linear infinite;
              }

              @keyframes rotate {
                from {
                  transform: rotate(0deg);
                }
                to {
                  transform: rotate(360deg);
                }
              }
            }
          }

          .file-preview {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 16px;
            color: #909399;

            p {
              margin: 0;
              font-size: 14px;
              text-align: center;
            }

            .file-size {
              font-size: 12px;
              color: #c9cdd4;
            }
          }

          .attachment-details {
            padding: 12px;
            border-top: 1px solid #e5e6eb;
            background-color: #fafafa;

            .detail-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              font-size: 12px;

              &:last-child {
                margin-bottom: 0;
              }

              .label {
                color: #909399;
                font-weight: 500;
              }

              .value {
                color: #1d2129;
                word-break: break-all;
              }
            }
          }
        }

        .no-attachment {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .review-section {
        .review-header {
          margin-bottom: 8px;

          label {
            font-size: 14px;
            color: #5c5c5c;
          }
        }

        .review-textarea {
          :deep(.el-textarea__inner) {
            min-height: 100px;
            border-radius: 2px;
          }
        }
      }
    }
  }
}
</style>
