// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
import isString from 'lodash/isString';
import merge from 'lodash/merge';
import { ElMessage } from 'element-plus';
import type { InternalAxiosRequestConfig } from 'axios';
import router from '@/router';
import type { AxiosTransform, CreateAxiosOptions } from './AxiosTransform';
import { VAxios } from './Axios';
import proxy from '@/config/proxy';
import { joinTimestamp, formatRequestDate, setObjToUrlParams } from './utils';
import { ContentTypeEnum } from '@/constants';
import 'element-plus/es/components/message/style/css';
import useUserStore from '@/store/modules/user';
const env = import.meta.env.MODE || 'development';

// 如果是mock模式 或 没启用直连代理 就不配置host 会走本地Mock拦截 或 Vite 代理
const host = env === 'mock' || !proxy.isRequestProxy ? '' : proxy[env].host;
// 数据处理，方便区分多种处理方式
const transform: AxiosTransform = {
  // 处理请求数据。如果数据不是预期格式，可直接抛出错误
  transformRequestHook: async (res, options) => {
    const { isTransformResponse, isReturnNativeResponse } = options;

    // 如果204无内容直接返回
    const method = res.config.method?.toLowerCase();
    if (res.status === 204 || method === 'put' || method === 'patch') {
      return res;
    }

    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformResponse) {
      return res.data;
    }
    // 错误的时候返回
    const { data } = res;
    if (!data) {
      throw new Error('请求接口错误');
    }

    //  这里 code为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
    const { code, msg, message } = data;

    if (code === 401 || code === 403) {
      ElMessage.error('您的登录已过期，请重新登录！');
      window.location.replace('http://' + location.hostname);
    }

    // 这里逻辑可以根据项目进行修改
    const hasSuccess = (data && (code == 200 || code === 0)) || res.config.responseType === 'blob';
    if (hasSuccess) {
      return Reflect.has(data, 'data') ? data.data : data;
    }
    if (msg || message || data.data.msg || data.data.message)
      ElMessage.error(msg || message || data.data.msg || data.data.message);
    throw new Error(`请求接口错误, 错误码: ${code}`);
  },

  // #region 请求前处理配置
  beforeRequestHook: (config, options) => {
    const {
      apiUrl,
      isJoinPrefix,
      urlPrefix,
      joinParamsToUrl,
      formatDate,
      joinTime = true,
    } = options;

    // 添加接口前缀
    if (isJoinPrefix && urlPrefix && isString(urlPrefix)) {
      if (!config.url?.startsWith('/xinfeng-screen')) {
        config.url = `${urlPrefix}${config.url}`;
      }
    }

    // 将baseUrl拼接
    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url}`;
    }
    const params = config.params || {};
    const data = config.data || false;

    if (formatDate && data && !isString(data)) {
      formatRequestDate(data);
    }
    if (config.method?.toUpperCase() === 'GET') {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, joinTimestamp(joinTime, false));
      } else {
        // 兼容restful风格
        config.url = `${config.url + params}${joinTimestamp(joinTime, true)}`;
        config.params = undefined;
      }
    } else if (!isString(params)) {
      if (formatDate) {
        formatRequestDate(params);
      }
      if (
        Reflect.has(config, 'data') &&
        config.data &&
        (Object.keys(config.data).length > 0 || data instanceof FormData)
      ) {
        config.data = data;
        config.params = params;
      } else {
        // 非GET请求如果没有提供data，则将params视为data
        config.data = params;
        config.params = undefined;
      }
      if (joinParamsToUrl) {
        config.url = setObjToUrlParams(config.url as string, {
          ...config.params,
          ...config.data,
        });
      }
    } else {
      // 兼容restful风格
      config.url += params;
      config.params = undefined;
    }
    return config;
  },
  // #endregion

  // 请求拦截器处理
  requestInterceptors: (config, options) => {
    // 请求之前处理config
    // const { token } = useUserStore();
    const { token, longToken } = useUserStore();
    if (token && (config as Recordable)?.requestOptions?.withToken !== false) {
      // 自定义
      (config as Recordable).headers.token = token;
      // 自定义
      // jwt token
      (config as Recordable).headers.Authorization = options.authenticationScheme
        ? `${options.authenticationScheme} ${token}`
        : token;

      (config as Recordable).headers.LongToken = longToken;
    }
    return config as InternalAxiosRequestConfig;
  },

  // #region 响应拦截器处理
  responseInterceptors: (res) => {
    // if (res.data.data === null) {
    //   return res;
    // } else if (!res.data.data) {
    //   throw new Error(res.data.message);
    // }
    if (res.headers.authorization) {
      useUserStore().userSetToken(res.headers.authorization)
      request.request(res.config)
    }
    return res;
  },
  // #endregion

  // 响应错误处理
  responseInterceptorsCatch: async (error: any) => {
    const { config, response } = error;
    if (!config && !response && error) {
      // ElMessage.error('用户未登录或已过期!')
      await useUserStore().userResetPasswordLogout(() => {
        router.replace('/login');
      });
    }

    if (response.status === 401 || response.status === 403) {
      // ElMessage.error('您的登录已过期，请重新登录！!');
      await useUserStore().userResetPasswordLogout(() => {
        router.replace('/login');
      });
      return Promise.reject(error);
    }

    if (error?.name === 'AxiosError') {
      ElMessage.error(error?.message);
      return Promise.reject(error);
    }

    if (!config || !config.requestOptions.retry) return Promise.reject(error);

    config.retryCount = config.retryCount || 0;

    if (config.retryCount >= config.requestOptions.retry.count) return Promise.reject(error);

    config.retryCount += 1;

    const backoff = new Promise((resolve) => {
      setTimeout(() => {
        resolve(config);
      }, config.requestOptions.retry.delay || 1);
    });
    config.headers = {
      ...config.headers,
      'Content-Type': ContentTypeEnum.Json,
    };
    return backoff.then((config: any) => request.request(config));
  },
};

function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    merge(
      <CreateAxiosOptions>{
        // authenticationScheme: 'Bearer',
        // 超时
        timeout: 6 * 1000,
        // 携带Cookie
        withCredentials: true,
        // 头信息
        headers: { 'Content-Type': ContentTypeEnum.Json },
        // 数据处理方式
        transform,
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 接口地址
          apiUrl: host,
          // 是否自动添加接口前缀
          isJoinPrefix: true,
          // 接口前缀
          // 例如: https://www.baidu.com/api
          // urlPrefix: '/api',
          urlPrefix: '/xinfeng-screen',
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreRepeatRequest: true,
          // 是否携带token
          withToken: true,
        },
      },

      opt || {}
    )
  );
}
export const request = createAxios();
