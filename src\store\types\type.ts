import type { RouteRecordRaw } from "vue-router";
//定义小仓库数据state类型
export interface UserState {
  token: string | null;
  menuRoutes: RouteRecordRaw[];
  username: string;
  avatar: string;
  buttons: string[];
}

//定义分类仓库state对象的ts类型

export interface loginRequestListI {
  tenantId: string;
  tenantName: string;
}

export interface encrypUserInfo {
  auth: boolean;
  systemCode: string;
  systemName: string;
  token: string;
  longToken: string;
  username: string;
  loginUserTenantList: loginRequestListI[];
}
