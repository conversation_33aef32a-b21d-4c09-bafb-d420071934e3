# PDF下载预览解决方案

## 🎯 功能概述

将PDF预览功能从直接URL访问改为模拟下载文件获取ArrayBuffer的方式，解决CORS跨域问题并提供更可靠的预览体验。

## 🔧 实现方案

### 1. 新增下载方式获取ArrayBuffer

**文件位置**：`src/utils/request/filePreview.ts`

#### 核心函数

```typescript
/**
 * 通过模拟下载方式获取文件ArrayBuffer
 * @param fullUrl 完整的文件URL
 * @returns Promise<ArrayBuffer> 文件的二进制数据
 */
export async function downloadFileAsArrayBuffer(fullUrl: string): Promise<ArrayBuffer> {
  try {
    console.log('模拟下载文件获取ArrayBuffer:', fullUrl);
    
    const response = await fetch(fullUrl, {
      method: 'GET',
      mode: 'cors', // 尝试CORS请求
      credentials: 'omit', // 不发送凭据
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    console.log('文件下载成功，大小:', arrayBuffer.byteLength, 'bytes');
    
    return arrayBuffer;
  } catch (error) {
    console.error('模拟下载文件失败:', error);
    throw new Error(`文件下载失败: ${error}`);
  }
}
```

#### 文件预览管理器增强

```typescript
/**
 * 通过下载方式创建文件预览URL
 * @param fullUrl 完整的文件URL
 * @param mimeType 文件MIME类型
 * @returns Promise<string> 预览URL
 */
async createPreviewUrlByDownload(fullUrl: string, mimeType: string = 'application/pdf'): Promise<string> {
  try {
    console.log('通过下载方式创建预览URL:', fullUrl);
    
    // 直接下载文件获取ArrayBuffer
    const arrayBuffer = await downloadFileAsArrayBuffer(fullUrl);
    
    // 创建Blob和Blob URL
    const blob = new Blob([arrayBuffer], { type: mimeType });
    const blobUrl = URL.createObjectURL(blob);
    
    // 记录生成的Blob URL，用于后续清理
    this.blobUrls.add(blobUrl);
    
    console.log('通过下载方式生成Blob URL成功:', blobUrl);
    return blobUrl;
  } catch (error) {
    console.error('通过下载方式创建预览URL失败:', error);
    throw error; // 不fallback，让调用方处理
  }
}
```

### 2. 修改预览组件逻辑

**文件位置**：`src/views/personnelQualificationPreview/index.vue`

#### 优化的PDF预览URL生成

```typescript
// 获取PDF预览URL - 通过下载方式获取ArrayBuffer
const getPdfPreviewUrl = async (url: string): Promise<string> => {
  if (!url) return "";

  try {
    // 如果是测试PDF，直接返回
    if (useTestPdf.value) {
      return "/pdfTest.pdf";
    }

    console.log("开始获取PDF预览URL:", url);
    isLoadingPdfPreview.value = true;

    // 优先尝试通过下载方式获取ArrayBuffer
    try {
      console.log("尝试通过下载方式获取PDF:", url);
      const previewUrl = await filePreviewManager.createPreviewUrlByDownload(
        getFullUrl(url),
        "application/pdf"
      );
      pdfPreviewUrl.value = previewUrl;
      console.log("通过下载方式生成PDF预览URL成功:", previewUrl);
      return previewUrl;
    } catch (downloadError) {
      console.warn("下载方式失败，尝试API方式:", downloadError);
      
      // 下载方式失败，尝试API方式（如果是跨域文件）
      if (needsApiPreview(url)) {
        console.log("使用API方式获取PDF预览:", url);
        const previewUrl = await filePreviewManager.createPreviewUrl(
          url,
          "application/pdf"
        );
        pdfPreviewUrl.value = previewUrl;
        console.log("API方式生成PDF预览URL成功:", previewUrl);
        return previewUrl;
      } else {
        // 不是跨域文件，直接使用原URL
        console.log("使用原始URL:", url);
        return getFullUrl(url);
      }
    }
  } catch (error) {
    console.error("获取PDF预览URL失败:", error);
    // 最终fallback到原URL
    return getFullUrl(url);
  } finally {
    isLoadingPdfPreview.value = false;
  }
};
```

## 📊 处理流程

### 新的处理流程（优先下载方式）

```
1. 检查是否为测试PDF → 直接返回 /pdfTest.pdf
2. 尝试下载方式获取ArrayBuffer
   ├─ 成功 → 创建Blob URL → 预览
   └─ 失败 → 继续下一步
3. 检查是否需要API预览
   ├─ 是 → 使用API方式获取 → 预览
   └─ 否 → 使用原始URL → 预览
4. 所有方式失败 → fallback到原始URL
```

### 优势对比

| 方式 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **下载方式** | 直接获取文件数据，无需后端API | 可能遇到CORS限制 | 文件服务器支持CORS |
| **API方式** | 绕过CORS限制 | 需要后端支持 | 严格的跨域环境 |
| **原始URL** | 最简单，无额外处理 | 可能有CORS问题 | 同域或无CORS限制 |

## 🔍 技术细节

### Fetch API配置

```typescript
const response = await fetch(fullUrl, {
  method: 'GET',
  mode: 'cors', // 尝试CORS请求
  credentials: 'omit', // 不发送凭据，减少CORS复杂性
});
```

**关键配置说明**：
- `mode: 'cors'`：明确指定CORS模式
- `credentials: 'omit'`：不发送认证信息，避免预检请求复杂化

### ArrayBuffer处理

```typescript
const arrayBuffer = await response.arrayBuffer();
const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
const blobUrl = URL.createObjectURL(blob);
```

**处理步骤**：
1. 获取响应的ArrayBuffer数据
2. 创建指定MIME类型的Blob对象
3. 生成可用于预览的Blob URL

### 错误处理策略

```typescript
try {
  // 尝试下载方式
  return await downloadMethod();
} catch (downloadError) {
  try {
    // 尝试API方式
    return await apiMethod();
  } catch (apiError) {
    // 最终fallback
    return originalUrl;
  }
}
```

**多层级fallback**：
1. 下载方式失败 → API方式
2. API方式失败 → 原始URL
3. 确保在任何情况下都有可用的预览方案

## 🎨 用户体验

### 加载状态管理

```vue
<!-- 加载状态 -->
<div v-if="isLoadingPdfPreview" class="pdf-loading">
  <el-icon class="is-loading"><Loading /></el-icon>
  <span>正在加载PDF预览...</span>
</div>

<!-- PDF预览组件 -->
<vue-office-pdf
  v-else
  :src="currentPdfUrl"
  style="width: 100%; height: 100%"
  @rendered="onPdfRendered"
  @error="onPdfError"
/>
```

### 控制台日志

提供详细的调试信息：
```
开始获取PDF预览URL: http://************:8090/...
尝试通过下载方式获取PDF: http://************:8090/...
模拟下载文件获取ArrayBuffer: http://************:8090/...
文件下载成功，大小: 1048576 bytes
通过下载方式生成Blob URL成功: blob:http://localhost:8853/uuid
```

## 🧪 测试场景

### 1. 下载方式成功
- 文件服务器支持CORS
- 直接下载获取ArrayBuffer
- 生成Blob URL预览

### 2. 下载方式失败，API方式成功
- 文件服务器不支持CORS
- 下载失败，使用API获取
- 通过后端代理预览

### 3. 所有方式失败
- 网络问题或服务器错误
- Fallback到原始URL
- 可能显示CORS错误，但不会崩溃

### 4. 测试PDF文件
- `useTestPdf = true`
- 直接使用 `/pdfTest.pdf`
- 跳过所有网络请求

## ✅ 兼容性保证

### 向后兼容
- 保持原有的API预览方式作为fallback
- 不影响图片预览和文件下载功能
- 支持多附件切换

### 渐进增强
- 优先使用最直接的下载方式
- 失败时自动降级到其他方式
- 确保在各种网络环境下都能工作

## 🎯 预期效果

实施后的改进：
1. ✅ **更可靠的预览**：多种方式确保PDF能够预览
2. ✅ **更好的性能**：直接下载避免额外的API调用
3. ✅ **更强的兼容性**：支持各种网络环境和服务器配置
4. ✅ **更清晰的调试**：详细的日志帮助问题定位
5. ✅ **更优雅的降级**：多层级fallback确保功能可用

现在PDF预览功能已经升级为更强大和可靠的下载预览方案！
