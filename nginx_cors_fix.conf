# Nginx CORS配置解决方案
# 针对 http://************:8090 服务器的CORS配置

server {
    listen 8090;
    server_name ************;
    
    # 全局CORS配置
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Encoding,Accept-Language,Connection,Host,Referer" always;
    add_header Access-Control-Expose-Headers "Content-Length,Content-Range,Content-Type,Date,Server,Transfer-Encoding" always;
    add_header Access-Control-Allow-Credentials "true" always;
    
    # 处理预检请求 (OPTIONS)
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Accept-Encoding,Accept-Language,Connection,Host,Referer";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Type 'text/plain; charset=utf-8';
        add_header Content-Length 0;
        return 204;
    }
    
    # 文件服务配置
    location /object/ {
        # 特别针对PDF文件的CORS配置
        location ~* \.pdf$ {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Range, Content-Type, Accept, Authorization" always;
            add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
            
            # 设置正确的MIME类型
            add_header Content-Type "application/pdf";
            
            # 允许范围请求（用于PDF流式加载）
            add_header Accept-Ranges "bytes";
            
            # 缓存配置
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }
        
        # 其他文件类型
        try_files $uri $uri/ =404;
        
        # 通用文件CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
    }
    
    # 根目录配置
    location / {
        try_files $uri $uri/ =404;
        
        # 通用CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日志配置
    access_log /var/log/nginx/cors_access.log;
    error_log /var/log/nginx/cors_error.log;
}

# 如果您的服务器还处理其他端口，也需要类似配置
server {
    listen 80;
    server_name ************;
    
    # 重定向到8090端口或添加相同的CORS配置
    return 301 http://$server_name:8090$request_uri;
}
