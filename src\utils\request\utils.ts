import isString from "lodash/isString";
import isObject from "lodash/isObject";

const DATE_TIME_FORMAT = "YYYY-MM-DD HH:mm:ss";

export function joinTimestamp<T extends boolean>(
  join: boolean,
  restful: T
): T extends true ? string : object;

export function joinTimestamp(join: boolean, restful = false): string | object {
  if (!join) {
    return restful ? "" : {};
  }
  const now = new Date().getTime();
  if (restful) {
    return `?_t=${now}`;
  }
  return { _t: now };
}

// 格式化提交参数时间
export function formatRequestDate(params: Recordable) {
  if (Object.prototype.toString.call(params) !== "[object Object]") {
    return;
  }

  for (const key in params) {
    // eslint-disable-next-line no-underscore-dangle
    if (params[key] && params[key]._isAMomentObject) {
      params[key] = params[key].format(DATE_TIME_FORMAT);
    }
    if (isString(key)) {
      const value = params[key];
      if (value) {
        try {
          params[key] = isString(value) ? value.trim() : value;
        } catch (error: any) {
          throw new Error(error);
        }
      }
    }
    if (isObject(params[key])) {
      formatRequestDate(params[key]);
    }
  }
}

// 将对象转为Url参数
export function setObjToUrlParams(baseUrl: string, obj: object): string {
  let parameters = "";
  for (const key in obj) {
    parameters += `${key}=${encodeURIComponent(obj[key as keyof typeof obj])}&`;
  }
  parameters = parameters.replace(/&$/, "");
  return /\?$/.test(baseUrl)
    ? baseUrl + parameters
    : baseUrl.replace(/\/?$/, "?") + parameters;
}

export function scroll(tableBody: any) {
  let isScroll = true;

  const dom1 = tableBody.getElementsByClassName("el-scrollbar__wrap")[0];

  //鼠标放上去，停止滚动；移开，继续滚动
  dom1.addEventListener("mouseover", () => {
    isScroll = false;
  });
  dom1.addEventListener("mouseout", () => {
    isScroll = true;
  });

  setInterval(() => {
    if (isScroll) {
      dom1.scrollTop += 2;
      if (dom1.clientHeight + dom1.scrollTop + 5 >= dom1.scrollHeight) {
        dom1.scrollTop = 0;
      }
    }
  }, 100);
}
