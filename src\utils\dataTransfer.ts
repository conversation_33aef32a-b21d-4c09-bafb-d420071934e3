/**
 * 数据传输工具类
 * 用于处理大数据量的页面间传输问题
 */

export interface DataTransferOptions {
  /** 存储键名 */
  storageKey?: string;
  /** 是否使用压缩 */
  useCompression?: boolean;
  /** URL参数名 */
  urlParamName?: string;
}

/**
 * 安全的URL编码，处理特殊字符
 * @param data 要编码的数据
 * @returns 编码后的字符串
 */
export function safeUrlEncode(data: any): string {
  try {
    const jsonString = JSON.stringify(data);
    // 使用encodeURIComponent确保所有特殊字符都被正确编码
    return encodeURIComponent(jsonString);
  } catch (error) {
    console.error('URL编码失败:', error);
    throw new Error('数据编码失败');
  }
}

/**
 * 安全的URL解码
 * @param encodedData 编码的数据
 * @returns 解码后的数据
 */
export function safeUrlDecode(encodedData: string): any {
  try {
    const decodedString = decodeURIComponent(encodedData);
    return JSON.parse(decodedString);
  } catch (error) {
    console.error('URL解码失败:', error);
    throw new Error('数据解码失败');
  }
}

/**
 * 检查URL长度是否超出限制
 * @param url URL字符串
 * @returns 是否超出限制
 */
export function isUrlTooLong(url: string): boolean {
  // 大多数浏览器的URL长度限制在2048-8192字符之间
  // 这里使用保守的2048字符限制
  return url.length > 2048;
}

/**
 * 数据传输管理器
 */
export class DataTransferManager {
  private defaultOptions: DataTransferOptions = {
    storageKey: 'dataTransfer',
    useCompression: false,
    urlParamName: 'data'
  };

  constructor(private options: DataTransferOptions = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  /**
   * 准备数据传输
   * 根据数据大小自动选择最佳传输方式
   * @param data 要传输的数据
   * @param baseUrl 基础URL
   * @returns 传输信息
   */
  prepareTransfer(data: any, baseUrl: string): {
    url: string;
    method: 'url' | 'storage';
    warning?: string;
  } {
    try {
      // 尝试URL传输
      const encodedData = safeUrlEncode(data);
      const separator = baseUrl.includes('?') ? '&' : '?';
      const testUrl = `${baseUrl}${separator}${this.options.urlParamName}=${encodedData}`;

      if (!isUrlTooLong(testUrl)) {
        return {
          url: testUrl,
          method: 'url'
        };
      }

      // URL太长，使用sessionStorage
      const storageKey = this.options.storageKey!;
      sessionStorage.setItem(storageKey, JSON.stringify(data));
      
      const storageUrl = `${baseUrl}${separator}useStorage=true&storageKey=${storageKey}`;
      
      return {
        url: storageUrl,
        method: 'storage',
        warning: `数据量过大(${encodedData.length}字符)，已使用sessionStorage传输`
      };

    } catch (error) {
      console.error('准备数据传输失败:', error);
      throw new Error('数据传输准备失败');
    }
  }

  /**
   * 接收数据
   * 自动检测传输方式并获取数据
   * @param urlParams URL参数对象
   * @returns 接收到的数据
   */
  receiveData(urlParams: URLSearchParams | Record<string, string>): any {
    try {
      const params = urlParams instanceof URLSearchParams ? 
        Object.fromEntries(urlParams.entries()) : urlParams;

      // 检查是否使用了storage传输
      if (params.useStorage === 'true' && params.storageKey) {
        return this.loadFromStorage(params.storageKey);
      }

      // 尝试从URL参数获取数据
      const dataParam = params[this.options.urlParamName!];
      if (dataParam) {
        return safeUrlDecode(dataParam);
      }

      // 尝试从默认storage key加载
      return this.loadFromStorage(this.options.storageKey!);

    } catch (error) {
      console.error('接收数据失败:', error);
      throw new Error('数据接收失败');
    }
  }

  /**
   * 从storage加载数据
   * @param storageKey 存储键名
   * @returns 加载的数据
   */
  private loadFromStorage(storageKey: string): any {
    const storedData = sessionStorage.getItem(storageKey);
    if (!storedData) {
      throw new Error(`未找到存储的数据: ${storageKey}`);
    }
    return JSON.parse(storedData);
  }

  /**
   * 清理存储的数据
   * @param storageKey 存储键名
   */
  cleanup(storageKey?: string): void {
    const key = storageKey || this.options.storageKey!;
    sessionStorage.removeItem(key);
    console.log(`已清理存储数据: ${key}`);
  }
}

/**
 * 默认数据传输管理器实例
 */
export const defaultDataTransfer = new DataTransferManager({
  storageKey: 'personnelQualificationData',
  urlParamName: 'data'
});

/**
 * 检测URL参数是否被截断
 * @param jsonString JSON字符串
 * @returns 是否被截断
 */
export function detectTruncation(jsonString: string): {
  isTruncated: boolean;
  reason?: string;
} {
  const trimmed = jsonString.trim();
  
  // 检查JSON结构完整性
  if (!trimmed.startsWith('[') && !trimmed.startsWith('{')) {
    return { isTruncated: true, reason: '数据不以[或{开头' };
  }
  
  if (!trimmed.endsWith(']') && !trimmed.endsWith('}')) {
    return { isTruncated: true, reason: '数据不以]或}结尾，可能被#字符截断' };
  }
  
  // 尝试解析JSON
  try {
    JSON.parse(trimmed);
    return { isTruncated: false };
  } catch (error) {
    return { 
      isTruncated: true, 
      reason: `JSON解析失败: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}
