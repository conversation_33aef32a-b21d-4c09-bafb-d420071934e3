import { request } from "@/utils/request";

// 机组实时负荷
export const getUnitFuDTO = () => {
  const res = request.get({
    url: "/screenQuery/getUnitFuDTO",
  });
  return res;
};

//机组平均负荷
export const getFuHe = () => {
  const res = request.get({
    url: "/screenQuery/getFuHe",
  });
  return res;
};

// 生产用电率
export const getUnitRateDTO = () => {
  const res = request.get({
    url: "/screenQuery/getUnitRateDTO",
  });
  return res;
};

// 本月发电量
export const getMonEl = () => {
  const res = request.get({
    url: "/screenQuery/getMonEl",
  });
  return res;
};

// 每月发电量
export const getEveryMonElDTO = () => {
  const res = request.get({
    url: "/screenQuery/getEveryMonElDTO",
  });
  return res;
};

// 计划完成情况
export const getPlanElDTO = () => {
  const res = request.get({
    url: "/screenQuery/getPlanElDTO",
  });
  return res;
};

// 煤炭储量
export const getMeitanDTO = () => {
  const res = request.get({
    url: "/screenQuery/getMeitanDTO",
  });
  return res;
};

// 污染物排放情况
export const getWuRanDTO = () => {
  const res = request.get({
    url: "/screenQuery/getWuRanDTO",
  });
  return res;
};
// 首页头部接口
export const getMeitangetHeadDataDTO = () => {
  const res = request.get({
    url: "/screenQuery/getMeitangetHeadDataDTO",
  });
  return res;
};
// 首页中部
export const getUnitCenterDTO = () => {
  const res = request.get({
    url: "/screenQuery/getUnitCenterDTO",
  });
  return res;
};
